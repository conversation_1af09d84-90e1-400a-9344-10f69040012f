/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.tableagg;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.TableAggregateFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

public class LastNonNullValuesAggNew
    extends TableAggregateFunction<Row, LastNonNullValuesAggNew.LastNonNullValuesAccumulator> {

  @Override
  public LastNonNullValuesAccumulator createAccumulator() {
    return new LastNonNullValuesAccumulator();
  }

  public
  @DataTypeHint("ROW<"
      + "ts TIMESTAMP(3), "
      + "total_fuel_consumption DECIMAL(38, 6), "
      + "total_fuel_consumption_time BIGINT, "
      + "working_time DECIMAL(38, 6), "
      + "working_time_time BIGINT, "
      + "engine_worktime DECIMAL(38, 6), "
      + "engine_worktime_time BIGINT, "
      + "pumping_volume DECIMAL(38, 6), "
      + "pumping_volume_time BIGINT, "
      + "driving_mileage DECIMAL(38, 6), "
      + "driving_mileage_time BIGINT, "
      + "travel_speed DECIMAL(38, 6), "
      + "travel_speed_time BIGINT, "
      + "water_temperature DECIMAL(38, 6), "
      + "water_temperature_time BIGINT, "
      + "fuel_level DECIMAL(38, 6), "
      + "fuel_level_time BIGINT, "
      + "engine_speed DECIMAL(38, 6), "
      + "engine_speed_time BIGINT, "
      + "SOC_stateofcharge DECIMAL(38, 6), "
      + "SOC_stateofcharge_time BIGINT, "
      + "total_electric_consumption DECIMAL(38, 6), "
      + "total_electric_consumption_time BIGINT, "
      + "longitude DECIMAL(38, 6), "
      + "latitude DECIMAL(38, 6), "
      + "device_location_time BIGINT>")
  void accumulate(LastNonNullValuesAccumulator acc,
                  @DataTypeHint("TIMESTAMP(3)") Timestamp ts,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_fuel_consumption_context_this_pv,
                  @DataTypeHint("BIGINT") Long total_fuel_consumption_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal working_time_context_this_pv,
                  @DataTypeHint("BIGINT") Long working_time_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal engine_worktime_context_this_pv,
                  @DataTypeHint("BIGINT") Long engine_worktime_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal pumping_volume_context_this_pv,
                  @DataTypeHint("BIGINT") Long pumping_volume_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal driving_mileage_context_this_pv,
                  @DataTypeHint("BIGINT") Long driving_mileage_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal travel_speed_context_this_pv,
                  @DataTypeHint("BIGINT") Long travel_speed_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal water_temperature_context_this_pv,
                  @DataTypeHint("BIGINT") Long water_temperature_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal fuel_level_context_this_pv,
                  @DataTypeHint("BIGINT") Long fuel_level_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal engine_speed_context_this_pv,
                  @DataTypeHint("BIGINT") Long engine_speed_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal soc_stateofcharge_context_this_pv,
                  @DataTypeHint("BIGINT") Long soc_stateofcharge_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)")
                      BigDecimal total_electric_consumption_context_this_pv,
                  @DataTypeHint("BIGINT") Long total_electric_consumption_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal device_location_context_this_longitude,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal device_location_context_this_latitude,
                  @DataTypeHint("BIGINT") Long device_location_context_this_pt) {

    if (ts == null) {
      return;
    }
    acc.setTs(ts);
    if (total_fuel_consumption_context_this_pv != null) {
      acc.setTotalFuelConsumption(total_fuel_consumption_context_this_pv);
      acc.setTotalFuelConsumptionTime(total_fuel_consumption_context_this_pt);
    }
    if (working_time_context_this_pv != null) {
      acc.setWorkingTime(working_time_context_this_pv);
      acc.setWorkingTimeTime(working_time_context_this_pt);
    }
    if (engine_worktime_context_this_pv != null) {
      acc.setEngineWorktime(engine_worktime_context_this_pv);
      acc.setEngineWorktimeTime(engine_worktime_context_this_pt);
    }
    if (pumping_volume_context_this_pv != null) {
      acc.setPumpingVolume(pumping_volume_context_this_pv);
      acc.setPumpingVolumeTime(pumping_volume_context_this_pt);
    }
    if (driving_mileage_context_this_pv != null) {
      acc.setDrivingMileage(driving_mileage_context_this_pv);
      acc.setDrivingMileageTime(driving_mileage_context_this_pt);
    }
    if (travel_speed_context_this_pv != null) {
      acc.setTravelSpeed(travel_speed_context_this_pv);
      acc.setTravelSpeedTime(travel_speed_context_this_pt);
    }
    if (water_temperature_context_this_pv != null) {
      acc.setWaterTemperature(water_temperature_context_this_pv);
      acc.setWaterTemperatureTime(water_temperature_context_this_pt);
    }
    if (fuel_level_context_this_pv != null) {
      acc.setFuelLevel(fuel_level_context_this_pv);
      acc.setFuelLevelTime(fuel_level_context_this_pt);
    }
    if (engine_speed_context_this_pv != null) {
      acc.setEngineSpeed(engine_speed_context_this_pv);
      acc.setEngineSpeedTime(engine_speed_context_this_pt);
    }
    if (soc_stateofcharge_context_this_pv != null) {
      acc.setSocStateOfCharge(soc_stateofcharge_context_this_pv);
      acc.setSocStateOfChargeTime(soc_stateofcharge_context_this_pt);
    }
    if (total_electric_consumption_context_this_pv != null) {
      acc.setTotalElectricConsumption(total_electric_consumption_context_this_pv);
      acc.setTotalElectricConsumptionTime(total_electric_consumption_context_this_pt);
    }
    if (device_location_context_this_longitude != null &&
        device_location_context_this_latitude != null) {
      acc.setLongitude(device_location_context_this_longitude);
      acc.setLatitude(device_location_context_this_latitude);
      acc.setDeviceLocationTime(device_location_context_this_pt);
    }
  }

  public
  @DataTypeHint("ROW<"
      + "ts TIMESTAMP(3), "
      + "total_fuel_consumption DECIMAL(38, 6), "
      + "total_fuel_consumption_time BIGINT, "
      + "working_time DECIMAL(38, 6), "
      + "working_time_time BIGINT, "
      + "engine_worktime DECIMAL(38, 6), "
      + "engine_worktime_time BIGINT, "
      + "pumping_volume DECIMAL(38, 6), "
      + "pumping_volume_time BIGINT, "
      + "driving_mileage DECIMAL(38, 6), "
      + "driving_mileage_time BIGINT, "
      + "travel_speed DECIMAL(38, 6), "
      + "travel_speed_time BIGINT, "
      + "water_temperature DECIMAL(38, 6), "
      + "water_temperature_time BIGINT, "
      + "fuel_level DECIMAL(38, 6), "
      + "fuel_level_time BIGINT, "
      + "engine_speed DECIMAL(38, 6), "
      + "engine_speed_time BIGINT, "
      + "SOC_stateofcharge DECIMAL(38, 6), "
      + "SOC_stateofcharge_time BIGINT, "
      + "total_electric_consumption DECIMAL(38, 6), "
      + "total_electric_consumption_time BIGINT, "
      + "longitude DECIMAL(38, 6), "
      + "latitude DECIMAL(38, 6), "
      + "device_location_time BIGINT>")
  void emitValue(LastNonNullValuesAccumulator accumulator, Collector<Row> out) {
    Timestamp timestamp = accumulator.getTs();
    LocalDateTime localDateTime = timestamp.toLocalDateTime();
    out.collect(Row.of(
        localDateTime,
        accumulator.getTotalFuelConsumption(),
        accumulator.getTotalFuelConsumptionTime(),
        accumulator.getWorkingTime(),
        accumulator.getWorkingTimeTime(),
        accumulator.getEngineWorktime(),
        accumulator.getEngineWorktimeTime(),
        accumulator.getPumpingVolume(),
        accumulator.getPumpingVolumeTime(),
        accumulator.getDrivingMileage(),
        accumulator.getDrivingMileageTime(),
        accumulator.getTravelSpeed(),
        accumulator.getTravelSpeedTime(),
        accumulator.getWaterTemperature(),
        accumulator.getWaterTemperatureTime(),
        accumulator.getFuelLevel(),
        accumulator.getFuelLevelTime(),
        accumulator.getEngineSpeed(),
        accumulator.getEngineSpeedTime(),
        accumulator.getSocStateOfCharge(),
        accumulator.getSocStateOfChargeTime(),
        accumulator.getTotalElectricConsumption(),
        accumulator.getTotalElectricConsumptionTime(),
        accumulator.getLongitude(),
        accumulator.getLatitude(),
        accumulator.getDeviceLocationTime()
    ));
  }

  public
  @DataTypeHint("ROW<"
      + "ts TIMESTAMP(3), "
      + "total_fuel_consumption DECIMAL(38, 6), "
      + "total_fuel_consumption_time BIGINT, "
      + "working_time DECIMAL(38, 6), "
      + "working_time_time BIGINT, "
      + "engine_worktime DECIMAL(38, 6), "
      + "engine_worktime_time BIGINT, "
      + "pumping_volume DECIMAL(38, 6), "
      + "pumping_volume_time BIGINT, "
      + "driving_mileage DECIMAL(38, 6), "
      + "driving_mileage_time BIGINT, "
      + "travel_speed DECIMAL(38, 6), "
      + "travel_speed_time BIGINT, "
      + "water_temperature DECIMAL(38, 6), "
      + "water_temperature_time BIGINT, "
      + "fuel_level DECIMAL(38, 6), "
      + "fuel_level_time BIGINT, "
      + "engine_speed DECIMAL(38, 6), "
      + "engine_speed_time BIGINT, "
      + "SOC_stateofcharge DECIMAL(38, 6), "
      + "SOC_stateofcharge_time BIGINT, "
      + "total_electric_consumption DECIMAL(38, 6), "
      + "total_electric_consumption_time BIGINT, "
      + "longitude DECIMAL(38, 6), "
      + "latitude DECIMAL(38, 6), "
      + "device_location_time BIGINT>")
  void retract(LastNonNullValuesAccumulator acc,
               @DataTypeHint("TIMESTAMP(3)") Timestamp ts,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_fuel_consumption_context_this_pv,
               @DataTypeHint("BIGINT") Long total_fuel_consumption_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal working_time_context_this_pv,
               @DataTypeHint("BIGINT") Long working_time_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal engine_worktime_context_this_pv,
               @DataTypeHint("BIGINT") Long engine_worktime_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal pumping_volume_context_this_pv,
               @DataTypeHint("BIGINT") Long pumping_volume_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal driving_mileage_context_this_pv,
               @DataTypeHint("BIGINT") Long driving_mileage_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal travel_speed_context_this_pv,
               @DataTypeHint("BIGINT") Long travel_speed_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal water_temperature_context_this_pv,
               @DataTypeHint("BIGINT") Long water_temperature_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal fuel_level_context_this_pv,
               @DataTypeHint("BIGINT") Long fuel_level_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal engine_speed_context_this_pv,
               @DataTypeHint("BIGINT") Long engine_speed_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal soc_stateofcharge_context_this_pv,
               @DataTypeHint("BIGINT") Long soc_stateofcharge_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)")
                   BigDecimal total_electric_consumption_context_this_pv,
               @DataTypeHint("BIGINT") Long total_electric_consumption_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal device_location_context_this_longitude,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal device_location_context_this_latitude,
               @DataTypeHint("BIGINT") Long device_location_context_this_pt) {

  }

  public void merge(LastNonNullValuesAccumulator acc,
                    Iterable<LastNonNullValuesAccumulator> iterable) {
    for (LastNonNullValuesAccumulator otherAcc : iterable) {
      acc.setTs(otherAcc.getTs());
      acc.setTotalFuelConsumption(otherAcc.getTotalFuelConsumption());
      acc.setTotalFuelConsumptionTime(otherAcc.getTotalFuelConsumptionTime());
      acc.setWorkingTime(otherAcc.getWorkingTime());
      acc.setWorkingTimeTime(otherAcc.getWorkingTimeTime());
      acc.setEngineWorktime(otherAcc.getEngineWorktime());
      acc.setEngineWorktimeTime(otherAcc.getEngineWorktimeTime());
      acc.setPumpingVolume(otherAcc.getPumpingVolume());
      acc.setPumpingVolumeTime(otherAcc.getPumpingVolumeTime());
      acc.setDrivingMileage(otherAcc.getDrivingMileage());
      acc.setDrivingMileageTime(otherAcc.getDrivingMileageTime());
      acc.setTravelSpeed(otherAcc.getTravelSpeed());
      acc.setTravelSpeedTime(otherAcc.getTravelSpeedTime());
      acc.setWaterTemperature(otherAcc.getWaterTemperature());
      acc.setWaterTemperatureTime(otherAcc.getWaterTemperatureTime());
      acc.setFuelLevel(otherAcc.getFuelLevel());
      acc.setFuelLevelTime(otherAcc.getFuelLevelTime());
      acc.setEngineSpeed(otherAcc.getEngineSpeed());
      acc.setEngineSpeedTime(otherAcc.getEngineSpeedTime());
      acc.setSocStateOfCharge(otherAcc.getSocStateOfCharge());
      acc.setSocStateOfChargeTime(otherAcc.getSocStateOfChargeTime());
      acc.setTotalElectricConsumption(otherAcc.getTotalElectricConsumption());
      acc.setTotalElectricConsumptionTime(otherAcc.getTotalElectricConsumptionTime());
      acc.setLongitude(otherAcc.getLongitude());
      acc.setLatitude(otherAcc.getLatitude());
      acc.setDeviceLocationTime(otherAcc.getDeviceLocationTime());
    }
  }

  public static class LastNonNullValuesAccumulator {
    private @DataTypeHint("TIMESTAMP(3)")
    Timestamp ts;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal total_fuel_consumption;
    private @DataTypeHint("BIGINT")
    Long total_fuel_consumption_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal working_time;
    private @DataTypeHint("BIGINT")
    Long working_time_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal engine_worktime;
    private @DataTypeHint("BIGINT")
    Long engine_worktime_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal pumping_volume;
    private @DataTypeHint("BIGINT")
    Long pumping_volume_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal driving_mileage;
    private @DataTypeHint("BIGINT")
    Long driving_mileage_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal travel_speed;
    private @DataTypeHint("BIGINT")
    Long travel_speed_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal water_temperature;
    private @DataTypeHint("BIGINT")
    Long water_temperature_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal fuel_level;
    private @DataTypeHint("BIGINT")
    Long fuel_level_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal engine_speed;
    private @DataTypeHint("BIGINT")
    Long engine_speed_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal soc_stateofcharge;
    private @DataTypeHint("BIGINT")
    Long soc_stateofcharge_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal total_electric_consumption;
    private @DataTypeHint("BIGINT")
    Long total_electric_consumption_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal longitude;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal latitude;
    private @DataTypeHint("BIGINT")
    Long device_location_time;

    public Timestamp getTs() {
      return ts;
    }

    public void setTs(Timestamp ts) {
      this.ts = ts;
    }

    public BigDecimal getTotalFuelConsumption() {
      return total_fuel_consumption;
    }

    public void setTotalFuelConsumption(BigDecimal totalFuelConsumption) {
      this.total_fuel_consumption = totalFuelConsumption;
    }

    public Long getTotalFuelConsumptionTime() {
      return total_fuel_consumption_time;
    }

    public void setTotalFuelConsumptionTime(Long totalFuelConsumptionTime) {
      this.total_fuel_consumption_time = totalFuelConsumptionTime;
    }

    public BigDecimal getWorkingTime() {
      return working_time;
    }

    public void setWorkingTime(BigDecimal workingTime) {
      this.working_time = workingTime;
    }

    public Long getWorkingTimeTime() {
      return working_time_time;
    }

    public void setWorkingTimeTime(Long workingTimeTime) {
      this.working_time_time = workingTimeTime;
    }

    public BigDecimal getEngineWorktime() {
      return engine_worktime;
    }

    public void setEngineWorktime(BigDecimal engineWorktime) {
      this.engine_worktime = engineWorktime;
    }

    public Long getEngineWorktimeTime() {
      return engine_worktime_time;
    }

    public void setEngineWorktimeTime(Long engineWorktimeTime) {
      this.engine_worktime_time = engineWorktimeTime;
    }

    public BigDecimal getPumpingVolume() {
      return pumping_volume;
    }

    public void setPumpingVolume(BigDecimal pumpingVolume) {
      this.pumping_volume = pumpingVolume;
    }

    public Long getPumpingVolumeTime() {
      return pumping_volume_time;
    }

    public void setPumpingVolumeTime(Long pumpingVolumeTime) {
      this.pumping_volume_time = pumpingVolumeTime;
    }

    public BigDecimal getDrivingMileage() {
      return driving_mileage;
    }

    public void setDrivingMileage(BigDecimal drivingMileage) {
      this.driving_mileage = drivingMileage;
    }

    public Long getDrivingMileageTime() {
      return driving_mileage_time;
    }

    public void setDrivingMileageTime(Long drivingMileageTime) {
      this.driving_mileage_time = drivingMileageTime;
    }

    public BigDecimal getTravelSpeed() {
      return travel_speed;
    }

    public void setTravelSpeed(BigDecimal travelSpeed) {
      this.travel_speed = travelSpeed;
    }

    public Long getTravelSpeedTime() {
      return travel_speed_time;
    }

    public void setTravelSpeedTime(Long travelSpeedTime) {
      this.travel_speed_time = travelSpeedTime;
    }

    public BigDecimal getWaterTemperature() {
      return water_temperature;
    }

    public void setWaterTemperature(BigDecimal waterTemperature) {
      this.water_temperature = waterTemperature;
    }

    public Long getWaterTemperatureTime() {
      return water_temperature_time;
    }

    public void setWaterTemperatureTime(Long waterTemperatureTime) {
      this.water_temperature_time = waterTemperatureTime;
    }

    public BigDecimal getFuelLevel() {
      return fuel_level;
    }

    public void setFuelLevel(BigDecimal fuelLevel) {
      this.fuel_level = fuelLevel;
    }

    public Long getFuelLevelTime() {
      return fuel_level_time;
    }

    public void setFuelLevelTime(Long fuelLevelTime) {
      this.fuel_level_time = fuelLevelTime;
    }

    public BigDecimal getEngineSpeed() {
      return engine_speed;
    }

    public void setEngineSpeed(BigDecimal engineSpeed) {
      this.engine_speed = engineSpeed;
    }

    public Long getEngineSpeedTime() {
      return engine_speed_time;
    }

    public void setEngineSpeedTime(Long engineSpeedTime) {
      this.engine_speed_time = engineSpeedTime;
    }

    public BigDecimal getSocStateOfCharge() {
      return soc_stateofcharge;
    }

    public void setSocStateOfCharge(BigDecimal socStateOfCharge) {
      this.soc_stateofcharge = socStateOfCharge;
    }

    public Long getSocStateOfChargeTime() {
      return soc_stateofcharge_time;
    }

    public void setSocStateOfChargeTime(Long socStateOfChargeTime) {
      this.soc_stateofcharge_time = socStateOfChargeTime;
    }

    public BigDecimal getTotalElectricConsumption() {
      return total_electric_consumption;
    }

    public void setTotalElectricConsumption(BigDecimal totalElectricConsumption) {
      this.total_electric_consumption = totalElectricConsumption;
    }

    public Long getTotalElectricConsumptionTime() {
      return total_electric_consumption_time;
    }

    public void setTotalElectricConsumptionTime(Long totalElectricConsumptionTime) {
      this.total_electric_consumption_time = totalElectricConsumptionTime;
    }

    public BigDecimal getLongitude() {
      return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
      this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
      return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
      this.latitude = latitude;
    }

    public Long getDeviceLocationTime() {
      return device_location_time;
    }

    public void setDeviceLocationTime(Long deviceLocationTime) {
      this.device_location_time = deviceLocationTime;
    }
  }

}