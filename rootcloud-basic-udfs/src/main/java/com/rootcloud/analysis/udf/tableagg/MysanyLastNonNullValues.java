/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.tableagg;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.TableAggregateFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

public class MysanyLastNonNullValues
    extends TableAggregateFunction<Row, MysanyLastNonNullValues.LastNonNullValuesAccumulator> {

  @Override
  public LastNonNullValuesAccumulator createAccumulator() {
    return new LastNonNullValuesAccumulator();
  }

  public
  @DataTypeHint("ROW<"
      + "ts TIMESTAMP(3), "
      + "total_idle_fuel_consumption DECIMAL(38, 6), "
      + "total_idle_fuel_consumption_time BIGINT, "
      + "total_idle_time DECIMAL(38, 6), "
      + "total_idle_time_time BIGINT, "
      + "total_time_left_moving DECIMAL(38, 6), "
      + "total_time_left_moving_time BIGINT, "
      + "total_time_right_moving DECIMAL(38, 6), "
      + "total_time_right_moving_time BIGINT, "
      + "gear DECIMAL(38, 6), "
      + "gear_time BIGINT, "
      + "oil_pressure DECIMAL(38, 6), "
      + "oil_pressure_time BIGINT, "
      + "pump_total_absorbed_torque DECIMAL(38, 6), "
      + "pump_total_absorbed_torque_time BIGINT>")
  void accumulate(LastNonNullValuesAccumulator acc,
                  @DataTypeHint("TIMESTAMP(3)") Timestamp ts,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_idle_fuel_consumption_context_this_pv,
                  @DataTypeHint("BIGINT") Long total_idle_fuel_consumption_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_idle_time_context_this_pv,
                  @DataTypeHint("BIGINT") Long total_idle_time_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_time_left_moving_context_this_pv,
                  @DataTypeHint("BIGINT") Long total_time_left_moving_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_time_right_moving_context_this_pv,
                  @DataTypeHint("BIGINT") Long total_time_right_moving_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal gear_context_this_pv,
                  @DataTypeHint("BIGINT") Long gear_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal oil_pressure_context_this_pv,
                  @DataTypeHint("BIGINT") Long oil_pressure_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal pump_total_absorbed_torque_context_this_pv,
                  @DataTypeHint("BIGINT") Long pump_total_absorbed_torque_context_this_pt) {

    if (ts == null) {
      return;
    }
    acc.setTs(ts);
    if (total_idle_fuel_consumption_context_this_pv != null) {
      acc.setTotal_idle_fuel_consumption(total_idle_fuel_consumption_context_this_pv);
      acc.setTotal_idle_fuel_consumption_time(total_idle_fuel_consumption_context_this_pt);
    }
    if (total_idle_time_context_this_pv != null) {
      acc.setTotal_idle_time(total_idle_time_context_this_pv);
      acc.setTotal_idle_time_time(total_idle_time_context_this_pt);
    }
    if (total_time_left_moving_context_this_pv != null) {
      acc.setTotal_time_left_moving(total_time_left_moving_context_this_pv);
      acc.setTotal_time_left_moving_time(total_time_left_moving_context_this_pt);
    }
    if (total_time_right_moving_context_this_pv != null) {
      acc.setTotal_time_right_moving(total_time_right_moving_context_this_pv);
      acc.setTotal_time_right_moving_time(total_time_right_moving_context_this_pt);
    }
    if (gear_context_this_pv != null) {
      acc.setGear(gear_context_this_pv);
      acc.setGear_time(gear_context_this_pt);
    }
    if (oil_pressure_context_this_pv != null) {
      acc.setOil_pressure(oil_pressure_context_this_pv);
      acc.setOil_pressure_time(oil_pressure_context_this_pt);
    }
    if (pump_total_absorbed_torque_context_this_pv != null) {
      acc.setPump_total_absorbed_torque(pump_total_absorbed_torque_context_this_pv);
      acc.setPump_total_absorbed_torque_time(pump_total_absorbed_torque_context_this_pt);
    }
  }

  public
  @DataTypeHint("ROW<"
      + "ts TIMESTAMP(3), "
      + "total_idle_fuel_consumption DECIMAL(38, 6), "
      + "total_idle_fuel_consumption_time BIGINT, "
      + "total_idle_time DECIMAL(38, 6), "
      + "total_idle_time_time BIGINT, "
      + "total_time_left_moving DECIMAL(38, 6), "
      + "total_time_left_moving_time BIGINT, "
      + "total_time_right_moving DECIMAL(38, 6), "
      + "total_time_right_moving_time BIGINT, "
      + "gear DECIMAL(38, 6), "
      + "gear_time BIGINT, "
      + "oil_pressure DECIMAL(38, 6), "
      + "oil_pressure_time BIGINT, "
      + "pump_total_absorbed_torque DECIMAL(38, 6), "
      + "pump_total_absorbed_torque_time BIGINT>")
  void emitValue(LastNonNullValuesAccumulator accumulator, Collector<Row> out) {
    Timestamp timestamp = accumulator.getTs();
    LocalDateTime localDateTime = timestamp.toLocalDateTime();
    out.collect(Row.of(
        localDateTime,
        accumulator.getTotal_idle_fuel_consumption(),
        accumulator.getTotal_idle_fuel_consumption_time(),
        accumulator.getTotal_idle_time(),
        accumulator.getTotal_idle_time_time(),
        accumulator.getTotal_time_left_moving(),
        accumulator.getTotal_time_left_moving_time(),
        accumulator.getTotal_time_right_moving(),
        accumulator.getTotal_time_right_moving_time(),
        accumulator.getGear(),
        accumulator.getGear_time(),
        accumulator.getOil_pressure(),
        accumulator.getOil_pressure_time(),
        accumulator.getPump_total_absorbed_torque(),
        accumulator.getPump_total_absorbed_torque_time()
    ));
  }

  public
  @DataTypeHint("ROW<"
      + "ts TIMESTAMP(3), "
      + "total_idle_fuel_consumption DECIMAL(38, 6), "
      + "total_idle_fuel_consumption_time BIGINT, "
      + "total_idle_time DECIMAL(38, 6), "
      + "total_idle_time_time BIGINT, "
      + "total_time_left_moving DECIMAL(38, 6), "
      + "total_time_left_moving_time BIGINT, "
      + "total_time_right_moving DECIMAL(38, 6), "
      + "total_time_right_moving_time BIGINT, "
      + "gear DECIMAL(38, 6), "
      + "gear_time BIGINT, "
      + "oil_pressure DECIMAL(38, 6), "
      + "oil_pressure_time BIGINT, "
      + "pump_total_absorbed_torque DECIMAL(38, 6), "
      + "pump_total_absorbed_torque_time BIGINT>")
  void retract(LastNonNullValuesAccumulator acc,
               @DataTypeHint("TIMESTAMP(3)") Timestamp ts,
              @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_idle_fuel_consumption_context_this_pv,
              @DataTypeHint("BIGINT") Long total_idle_fuel_consumption_context_this_pt,
              @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_idle_time_context_this_pv,
              @DataTypeHint("BIGINT") Long total_idle_time_context_this_pt,
              @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_time_left_moving_context_this_pv,
              @DataTypeHint("BIGINT") Long total_time_left_moving_context_this_pt,
              @DataTypeHint("DECIMAL(38, 6)") BigDecimal total_time_right_moving_context_this_pv,
              @DataTypeHint("BIGINT") Long total_time_right_moving_context_this_pt,
              @DataTypeHint("DECIMAL(38, 6)") BigDecimal gear_context_this_pv,
              @DataTypeHint("BIGINT") Long gear_context_this_pt,
              @DataTypeHint("DECIMAL(38, 6)") BigDecimal oil_pressure_context_this_pv,
              @DataTypeHint("BIGINT") Long oil_pressure_context_this_pt,
              @DataTypeHint("DECIMAL(38, 6)") BigDecimal pump_total_absorbed_torque_context_this_pv,
              @DataTypeHint("BIGINT") Long pump_total_absorbed_torque_context_this_pt) {

  }

  public void merge(LastNonNullValuesAccumulator acc,
                    Iterable<LastNonNullValuesAccumulator> iterable) {
    for (LastNonNullValuesAccumulator otherAcc : iterable) {
      acc.setTs(otherAcc.getTs());
      acc.setTotal_idle_fuel_consumption(otherAcc.getTotal_idle_fuel_consumption());
      acc.setTotal_idle_fuel_consumption_time(otherAcc.getTotal_idle_fuel_consumption_time());
      acc.setTotal_idle_time(otherAcc.getTotal_idle_time());
      acc.setTotal_idle_time_time(otherAcc.getTotal_idle_time_time());
      acc.setTotal_time_left_moving(otherAcc.getTotal_time_left_moving());
      acc.setTotal_time_left_moving_time(otherAcc.getTotal_time_left_moving_time());
      acc.setTotal_time_right_moving(otherAcc.getTotal_time_right_moving());
      acc.setTotal_time_right_moving_time(otherAcc.getTotal_time_right_moving_time());
      acc.setGear(otherAcc.getGear());
      acc.setGear_time(otherAcc.getGear_time());
      acc.setOil_pressure(otherAcc.getOil_pressure());
      acc.setOil_pressure_time(otherAcc.getOil_pressure_time());
      acc.setPump_total_absorbed_torque(otherAcc.getPump_total_absorbed_torque());
      acc.setPump_total_absorbed_torque_time(otherAcc.getPump_total_absorbed_torque_time());
    }
  }

  public static class LastNonNullValuesAccumulator {
    private @DataTypeHint("TIMESTAMP(3)")
    Timestamp ts;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal total_idle_fuel_consumption;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal total_idle_time;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal total_time_left_moving;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal total_time_right_moving;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal gear;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal oil_pressure;
    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal pump_total_absorbed_torque;
    private @DataTypeHint("BIGINT")
    Long total_idle_fuel_consumption_time;
    private @DataTypeHint("BIGINT")
    Long total_idle_time_time;
    private @DataTypeHint("BIGINT")
    Long total_time_left_moving_time;
    private @DataTypeHint("BIGINT")
    Long total_time_right_moving_time;
    private @DataTypeHint("BIGINT")
    Long gear_time;
    private @DataTypeHint("BIGINT")
    Long oil_pressure_time;
    private @DataTypeHint("BIGINT")
    Long pump_total_absorbed_torque_time;

    public Timestamp getTs() {
      return ts;
    }

    public void setTs(Timestamp ts) {
      this.ts = ts;
    }


    public BigDecimal getTotal_idle_fuel_consumption() {
      return total_idle_fuel_consumption;
    }

    public void setTotal_idle_fuel_consumption(BigDecimal total_idle_fuel_consumption) {
      this.total_idle_fuel_consumption = total_idle_fuel_consumption;
    }

    public BigDecimal getTotal_idle_time() {
      return total_idle_time;
    }

    public void setTotal_idle_time(BigDecimal total_idle_time) {
      this.total_idle_time = total_idle_time;
    }

    public BigDecimal getTotal_time_left_moving() {
      return total_time_left_moving;
    }

    public void setTotal_time_left_moving(BigDecimal total_time_left_moving) {
      this.total_time_left_moving = total_time_left_moving;
    }

    public BigDecimal getTotal_time_right_moving() {
      return total_time_right_moving;
    }

    public void setTotal_time_right_moving(BigDecimal total_time_right_moving) {
      this.total_time_right_moving = total_time_right_moving;
    }

    public BigDecimal getGear() {
      return gear;
    }

    public void setGear(BigDecimal gear) {
      this.gear = gear;
    }

    public BigDecimal getOil_pressure() {
      return oil_pressure;
    }

    public void setOil_pressure(BigDecimal oil_pressure) {
      this.oil_pressure = oil_pressure;
    }

    public BigDecimal getPump_total_absorbed_torque() {
      return pump_total_absorbed_torque;
    }

    public void setPump_total_absorbed_torque(BigDecimal pump_total_absorbed_torque) {
      this.pump_total_absorbed_torque = pump_total_absorbed_torque;
    }

    public Long getTotal_idle_fuel_consumption_time() {
      return total_idle_fuel_consumption_time;
    }

    public void setTotal_idle_fuel_consumption_time(Long total_idle_fuel_consumption_time) {
      this.total_idle_fuel_consumption_time = total_idle_fuel_consumption_time;
    }

    public Long getTotal_idle_time_time() {
      return total_idle_time_time;
    }

    public void setTotal_idle_time_time(Long total_idle_time_time) {
      this.total_idle_time_time = total_idle_time_time;
    }

    public Long getTotal_time_left_moving_time() {
      return total_time_left_moving_time;
    }

    public void setTotal_time_left_moving_time(Long total_time_left_moving_time) {
      this.total_time_left_moving_time = total_time_left_moving_time;
    }

    public Long getTotal_time_right_moving_time() {
      return total_time_right_moving_time;
    }

    public void setTotal_time_right_moving_time(Long total_time_right_moving_time) {
      this.total_time_right_moving_time = total_time_right_moving_time;
    }

    public Long getGear_time() {
      return gear_time;
    }

    public void setGear_time(Long gear_time) {
      this.gear_time = gear_time;
    }

    public Long getOil_pressure_time() {
      return oil_pressure_time;
    }

    public void setOil_pressure_time(Long oil_pressure_time) {
      this.oil_pressure_time = oil_pressure_time;
    }

    public Long getPump_total_absorbed_torque_time() {
      return pump_total_absorbed_torque_time;
    }

    public void setPump_total_absorbed_torque_time(Long pump_total_absorbed_torque_time) {
      this.pump_total_absorbed_torque_time = pump_total_absorbed_torque_time;
    }

  }

}