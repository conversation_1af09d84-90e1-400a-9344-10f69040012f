/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.table;

import java.math.BigDecimal;
import java.sql.Timestamp;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

public class TableRowFunction extends TableFunction<Row> {

  public @DataTypeHint("ROW<deviceId STRING, dt BIGINT, la DECIMAL(10, 4), laNew DECIMAL(10, 4)>")
  void eval(@DataTypeHint("STRING") String deviceId,
            @DataTypeHint("TIMESTAMP(9)") Timestamp dt,
            @DataTypeHint("DECIMAL(10, 5)") BigDecimal la) {
    if (la == null) return;
    collect(Row.of(deviceId, dt.getTime(), la, la.add(new BigDecimal(0.11))));
    collect(Row.of(deviceId, dt.getTime(), la, la.add(new BigDecimal(0.22))));
    collect(Row.of(deviceId, dt.getTime(), la, la.add(new BigDecimal(0.33))));
  }

}
