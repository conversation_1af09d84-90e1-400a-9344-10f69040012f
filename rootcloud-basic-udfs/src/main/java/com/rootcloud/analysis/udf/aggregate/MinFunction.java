/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.aggregate;

import java.math.BigDecimal;
import java.util.ArrayList;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.AggregateFunction;

public class MinFunction extends AggregateFunction<BigDecimal, MinFunction.MinAccumulator> {

  @Override
  public BigDecimal getValue(MinAccumulator accumulator) {
    if (accumulator.getValArray().isEmpty()) {
      return BigDecimal.valueOf(0);
    }
    return accumulator.getValArray().stream().min(BigDecimal::compareTo).get();
  }

  @Override
  public MinAccumulator createAccumulator() {
    return new MinAccumulator();
  }

  public @DataTypeHint("DECIMAL(38, 4)") void accumulate(MinAccumulator acc,
                  @DataTypeHint("DECIMAL(38, 4)")
                      BigDecimal bigDecimalVal) {
    acc.getValArray().add(bigDecimalVal);
  }

  public static class MinAccumulator {

    private @DataTypeHint("ARRAY<DECIMAL(38, 4)>")
    ArrayList<BigDecimal> valArray = new ArrayList<>();

    public ArrayList<BigDecimal> getValArray() {
      return valArray;
    }

    public void setValArray(ArrayList<BigDecimal> valArray) {
      this.valArray = valArray;
    }
  }
}
