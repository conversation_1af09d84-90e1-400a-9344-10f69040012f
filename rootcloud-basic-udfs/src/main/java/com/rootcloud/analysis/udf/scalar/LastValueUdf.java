package com.rootcloud.analysis.udf.scalar;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.ScalarFunction;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class LastValueUdf extends ScalarFunction {
    // 设备ID -> (属性名 -> 上一次非空值)
    private static final Map<String, Map<String, BigDecimal>> lastValueMap = new ConcurrentHashMap<>();

    @DataTypeHint("DECIMAL(38, 5)")
    public BigDecimal eval(@DataTypeHint("STRING") String deviceId, @DataTypeHint("STRING")  String paramName, @DataTypeHint("DECIMAL(38, 5)") BigDecimal paramValue) {
        if (deviceId == null || paramName == null) {
            return null;
        }
        // 获取该设备的属性Map
        Map<String, BigDecimal> deviceMap = lastValueMap.computeIfAbsent(deviceId, k -> new ConcurrentHashMap<>());
        BigDecimal lastValue = deviceMap.get(paramName);

        // 如果当前值非空，更新
        if (paramValue != null) {
            deviceMap.put(paramName, paramValue);
        }
        return lastValue;
    }

    public static void main(String[] args) {
        LastValueUdf udf = new LastValueUdf();
        String deviceId = "dev1";
        String paramName = "p1";

        // 第一次传入小数点后6位
        BigDecimal value1 = new BigDecimal("123.123456");
        BigDecimal result1 = udf.eval(deviceId, paramName, value1);
        System.out.println("第一次返回（应为null）: " + result1);

        // 第二次传入null，应该返回上一次的小数点后6位的值
        BigDecimal result2 = udf.eval(deviceId, paramName, null);
        System.out.println("第二次返回（应为123.123456）: " + result2);

        // 第三次传入小数点后5位
        BigDecimal value2 = new BigDecimal("456.12345");
        BigDecimal result3 = udf.eval(deviceId, paramName, value2);
        System.out.println("第三次返回（应为123.123456）: " + result3);

        // 第四次传入null，应该返回上一次的小数点后5位的值
        BigDecimal result4 = udf.eval(deviceId, paramName, null);
        System.out.println("第四次返回（应为456.12345）: " + result4);
    }
}