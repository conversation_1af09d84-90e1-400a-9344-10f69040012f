/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.tableagg;

import com.rootcloud.analysis.udf.util.DateUtil;
import org.apache.flink.api.java.tuple.Tuple7;
import org.apache.flink.api.java.tuple.Tuple9;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.TableAggregateFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.rootcloud.analysis.udf.util.DateUtil.TIME_PATTERN_03;

public class TableAggSumFunction
    extends TableAggregateFunction<Row, TableAggSumFunction.TableAggSumAccumulator> {

  private final String seperator = "###";

  @Override
  public TableAggSumAccumulator createAccumulator() {
    return new TableAggSumAccumulator();
  }

  @DataTypeHint("ROW<product_no STRING, stat_hour STRING, "
      + "rcSingleOutput_sum INTEGER, rcQualifieds_sum INTEGER, rcUnQualifieds_sum INTEGER, "
      + "optTime DECIMAL(10, 2), standyTime DECIMAL(10, 2), "
      + "faultTime DECIMAL(10, 2), shutdownTime DECIMAL(10, 2)>")
  public void accumulate(TableAggSumAccumulator acc,
                         @DataTypeHint("TIMESTAMP") Timestamp __timestamp__,
                         @DataTypeHint("STRING") String productNo,
                         @DataTypeHint("INTEGER") int status,
                         @DataTypeHint("DECIMAL(10, 2)") BigDecimal duration,
                         @DataTypeHint("INTEGER") int rcSingleOutput,
                         @DataTypeHint("INTEGER") int rcQualifieds,
                         @DataTypeHint("INTEGER") int rcUnQualifieds
  ) {
    acc.getInput().put(__timestamp__,
        productNo + seperator + status  + seperator + duration
            + seperator + rcSingleOutput + seperator + rcQualifieds + seperator + rcUnQualifieds
    );
  }

  public void retract(TableAggSumAccumulator acc,
                      Timestamp __timestamp__,
                      String productNo,
                      int status,
                      int duration,
                      int rcSingleOutput,
                      int rcQualifieds,
                      int rcUnQualifieds) {
    acc.getInput().remove(__timestamp__);
  }

  public void merge(TableAggSumAccumulator acc,
                    Iterable<TableAggSumAccumulator> iterable) {
    for (TableAggSumAccumulator otherAcc : iterable) {
      acc.getInput().putAll(otherAcc.getInput());
    }
  }

  public void emitValue(TableAggSumAccumulator acc, Collector<Row> out) {
    if (acc.getInput().isEmpty()) {
      return;
    }
    // 1.计算每行的差值.
    List<Timestamp> dtLst = acc.getInput().keySet()
        .stream().sorted(Timestamp::compareTo).collect(Collectors.toList());
    List<Tuple9<Long, String, Integer, Integer, Integer,
        BigDecimal, BigDecimal, BigDecimal, BigDecimal>> resultList = new LinkedList<>();
    Tuple9<Long, String, Integer, Integer, Integer,
        BigDecimal, BigDecimal, BigDecimal, BigDecimal> preVal = null;
    for (Timestamp currentDt : dtLst) {
      String[] array = acc.getInput().get(currentDt).split(seperator);
      if (array == null) {
        continue;
      }
      long dtLong = currentDt.getTime();
      String productNo = array[0] == null ? "" : (String) array[0];
      int status = Integer.valueOf(array[1]);
      BigDecimal duration = new BigDecimal(array[2]);
      BigDecimal optTime = status== 1 ? duration : BigDecimal.ZERO;
      BigDecimal standyTime = status== 2 ? duration : BigDecimal.ZERO;
      BigDecimal faultTime = status== 3 ? duration : BigDecimal.ZERO;
      BigDecimal shutdownTime = status== 4 ? duration : BigDecimal.ZERO;
      int rcSingleOutputCur = Integer.valueOf(array[3]);
      int rcQualifiedsCur = Integer.valueOf(array[4]);
      int rcUnQualifiedsCur = Integer.valueOf(array[5]);

      Tuple9<Long, String, Integer, Integer, Integer,
          BigDecimal, BigDecimal, BigDecimal, BigDecimal> result
          = new Tuple9(dtLong, productNo, 0, 0, 0,
          BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);;
      if (preVal != null) {
        int diff = rcSingleOutputCur - preVal.f2;
        if (diff > 0) {
          result.f2 += diff;
        }
        diff = rcQualifiedsCur - preVal.f3;
        if (diff > 0) {
          result.f3 += diff;
        }
        diff = rcUnQualifiedsCur - preVal.f4;
        if (diff > 0) {
          result.f4 += diff;
        }
      }
      if (optTime.compareTo(BigDecimal.ZERO) > 0) {
        result.f5 = result.f5.add(optTime);
      }
      if (standyTime.compareTo(BigDecimal.ZERO) > 0) {
        result.f6 = result.f6.add(standyTime);
      }
      if (faultTime.compareTo(BigDecimal.ZERO) > 0) {
        result.f7 = result.f7.add(faultTime);
      }
      if (shutdownTime.compareTo(BigDecimal.ZERO) > 0) {
        result.f8 = result.f8.add(shutdownTime);
      }
      resultList.add(result);
      preVal = new Tuple9(
          dtLong, productNo,
          rcSingleOutputCur, rcQualifiedsCur, rcUnQualifiedsCur,
          optTime, standyTime, faultTime, shutdownTime);
    }
    // 2.根据指定字段分组，并对差值进行汇总.
    Map<String, Tuple7<Integer, Integer, Integer,
        BigDecimal, BigDecimal, BigDecimal, BigDecimal>> resultMap =
        new HashMap<>();
    for (Tuple9<Long, String, Integer, Integer, Integer,
        BigDecimal, BigDecimal, BigDecimal, BigDecimal> data : resultList) {
      long dtLong = data.f0;
      String startHourStr = getStartHourStr(dtLong);
      String productNo = data.f1;
      // 分组Key
      String key = startHourStr + seperator + productNo;
      Tuple7<Integer, Integer, Integer,
          BigDecimal, BigDecimal, BigDecimal, BigDecimal> result = null;
      if (resultMap.containsKey(key)) {
        result = resultMap.get(key);
        result.f0 += data.f2;
        result.f1 += data.f3;
        result.f2 += data.f4;

        result.f3 = result.f3.add(data.f5);
        result.f4 = result.f4.add(data.f6);
        result.f5 = result.f5.add(data.f7);
        result.f6 = result.f6.add(data.f8);
      } else {
        result = new Tuple7(data.f2, data.f3, data.f4,
            data.f5, data.f6, data.f7, data.f8);
      }
      resultMap.put(key, result);
    }
    // 3.基于分组结果，按指定字段顺序输出到下游节点
    for (String key : resultMap.keySet()) {
      String[] temp = key.split(seperator);
      Tuple7<Integer, Integer, Integer,
          BigDecimal, BigDecimal, BigDecimal, BigDecimal> val = resultMap.get(key);
      out.collect(Row.of(
          temp[1], temp[0], val.f0, val.f1, val.f2,
          val.f3, val.f4, val.f5, val.f6));
    }
  }

  /**
   * 按指定时间分组规则来划分时间段.
   */
  private String getStartHourStr(long time) {
    long time1 = DateUtil.formatPatternLongToLong(time,
        "yyyy-MM-dd HH:mm:00", TIME_PATTERN_03);
    long time2 = DateUtil.formatPatternLongToLong(time,
        "yyyy-MM-dd HH:30:00", TIME_PATTERN_03);
    if (time1 < time2) {
      return DateUtil.formatPatternLongToString(time, "yyyy-MM-dd HH:00:00");
    } else {
      return DateUtil.formatPatternLongToString(time, "yyyy-MM-dd HH:30:00");
    }
  }

  public static class TableAggSumAccumulator {

    private Map<Timestamp, String> input = new HashMap<>();

    public Map<Timestamp, String> getInput() {
      return input;
    }

    public void setInput(Map<Timestamp, String> input) {
      this.input = input;
    }
  }
}
