/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.tableagg;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.rootcloud.analysis.udf.util.DateUtil;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.TableAggregateFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

public class TableStatusSumFunction
    extends TableAggregateFunction<Row, TableStatusSumFunction.TableStatusSumAccumulator> {

  @Override
  public TableStatusSumAccumulator createAccumulator() {
    return new TableStatusSumAccumulator();
  }

  public
  @DataTypeHint("ROW<target_status INTEGER, status_sum BIGINT>")
  void accumulate(TableStatusSumAccumulator acc,
                  @DataTypeHint("STRING") String dt,
                  @DataTypeHint("INTEGER") Integer status) {
    acc.getInput().put(dt, status);
  }

  public
  @DataTypeHint("ROW<target_status INTEGER, status_sum BIGINT>")
  void retract(TableStatusSumAccumulator acc,
               @DataTypeHint("STRING") String dt,
               @DataTypeHint("INTEGER") Integer status) {
    acc.getInput().remove(dt);
  }

  public void merge(TableStatusSumAccumulator acc,
                    java.lang.Iterable<TableStatusSumAccumulator> iterable) {
    for (TableStatusSumAccumulator otherAcc : iterable) {
      acc.getInput().putAll(otherAcc.getInput());
    }
  }

  public void emitValue(TableStatusSumAccumulator accumulator, Collector<Row> out) {
    Map<String, Integer> input = accumulator.getInput();
    if (input.isEmpty()) {
      return;
    }
    List<String> dtLst = input.keySet().stream().sorted(String::compareTo).collect(
        Collectors.toList());
    Map<Integer, Tuple2<String, Long>> result = new HashMap<>();
    for (String currentDt : dtLst) {
      Integer currentStatus = input.get(currentDt);
      if (currentStatus == null) {
        continue;
      }
      Tuple2<String, Long> val = null;
      if (result.containsKey(currentStatus)) {
        val = result.get(currentStatus);
        String preDt = val.f0;

        // Sum
        Long preSum = val.f1;
        long diff = (DateUtil.convertTimeToLong(currentDt)
                - DateUtil.convertTimeToLong(preDt)) / 1000;
        if (diff > 0) {
          val.f1 = preSum + diff;
        }

      } else {
        val = new Tuple2<>();
        val.f1 = 0L;

      }
      val.f0 = currentDt;
      result.put(currentStatus, val);
    }

    for (Integer status : result.keySet()) {
      out.collect(Row.of(
          status, result.get(status).f1));
    }
  }

  public static class TableStatusSumAccumulator {

    private @DataTypeHint("MAP<STRING,INTEGER>") Map<String, Integer> input = new HashMap<>();

    private @DataTypeHint("STRING") String bizDt;

    public Map<String, Integer> getInput() {
      return input;
    }

    public void setInput(Map<String, Integer> input) {
      this.input = input;
    }

    public String getBizDt() {
      return bizDt;
    }

    public void setBizDt(String bizDt) {
      this.bizDt = bizDt;
    }
  }
}
