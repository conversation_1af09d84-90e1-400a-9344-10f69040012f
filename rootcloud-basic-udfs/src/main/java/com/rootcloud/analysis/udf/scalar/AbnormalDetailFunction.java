/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.scalar;

import com.alibaba.fastjson.JSONObject;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.ScalarFunction;

public class AbnormalDetailFunction extends ScalarFunction {

  public @DataTypeHint("STRING") String eval(

      @DataTypeHint("BIGINT") Long this_pt,
      @DataTypeHint("DECIMAL(38, 6)") BigDecimal this_pv,
      @DataTypeHint("BIGINT") Long last_pt,
      @DataTypeHint("DECIMAL(38, 6)") BigDecimal last_pv) {

    if (this_pt == null || this_pv == null || last_pt == null || last_pv == null) {
      return null;
    }
    Long pt_inc = this_pt - last_pt;

    JSONObject result = new JSONObject();


    // 设置精度为小数点后三位，并使用四舍五入模式
    BigDecimal roundedThisPv = this_pv.setScale(2, RoundingMode.HALF_UP);
    BigDecimal roundedLastPv = last_pv.setScale(2, RoundingMode.HALF_UP);
    BigDecimal pv_inc = roundedThisPv.subtract(roundedLastPv);

    result.put("this_pv", roundedThisPv);
    result.put("this_pt", this_pt);
    result.put("last_pv", roundedLastPv);
    result.put("last_pt", last_pt);
    result.put("pv_inc", pv_inc);
    result.put("pt_inc", pt_inc);

    return result.toJSONString();
  }


}
