/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.scalar;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.rootcloud.analysis.udf.log.RcLogScalarFunction;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.flink.table.annotation.DataTypeHint;

public class VariablesBoundsFunction extends RcLogScalarFunction {

  /**
   * 从description中获取指定的属性id，例如:洗烘房温度实际值异常，当前值是%varibles.a%，正常区间>%variables.la_bounds.lower%
   * <%variables.la_bounds.upper% .  需要返回a,a_bounds.lower,a_bounds.upper。返回格式为字符串.
   */
  public @DataTypeHint("STRING")
  String eval(@DataTypeHint("STRING") String variables,
              @DataTypeHint("STRING") String description) {
    //从 description 中获取%xxx%格式的key
    JSONObject resultObj = new JSONObject();
    Set<String> keySet = null;
    Map<String, String> mapping = new HashMap<>();
    keySet = getVariableNames(description, mapping);
    try {
      JSONObject jsonObject = JSONObject.parseObject(variables);
      for (String key : keySet) {
        resultObj.put(key, JSONPath.eval(jsonObject, key));
      }
    } catch (Exception e) {
      rcLogError(String.format(
          "invoke VariablesBoundsTableRowFunction failed, variables:%s,description:%s,error msg:%s",
          variables, description, e.toString()));
    }
    //替换description中的占位符
    for (String key : resultObj.keySet()) {
      if (resultObj.getString(key) != null) {
        description = description.replaceAll("%(variables." + key + ")%", resultObj.getString(key));
      }
    }

    return description;

  }

  Set<String> getVariableNames(String input,
                               Map<String, String> mapping) {
    Set<String> names = new HashSet<>();
    Pattern pattern = Pattern.compile("%(.*?)%");
    Matcher matcher = pattern.matcher(input);
    while (matcher.find()) {
      String matchStr = matcher.group(1);

      if (matchStr.startsWith("variables")) {
        names.add(matchStr.replaceFirst("variables.", ""));
        mapping.put(matchStr.replaceFirst("variables.", ""), matchStr);
      }

    }
    return names;
  }
}
