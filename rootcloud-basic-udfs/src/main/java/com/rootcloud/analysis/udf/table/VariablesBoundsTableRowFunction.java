/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.table;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.rootcloud.analysis.udf.log.RcLogTableFunction;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.types.Row;

public class VariablesBoundsTableRowFunction extends RcLogTableFunction<Row> {

//  public static void main(String[] args) {
//    String desc
//        = "洗烘房温度实际值异常，当前值是%variables.lb%  %variables.la%，%variables.lb%正常区间>%variables.la_bounds.lower% <%variables.la_bounds.upper%";
//    String va = "{\"la_bounds\":\"{\\\"lower\\\":0,\\\"upper\\\":50}\",\"la\":78.87,\"lb\":\"11122233\"}";
//    eval(va, desc);
//  }

  static Set<String> getVariableNames(String input,
                                      Map<String, String> mapping, StringBuilder currentKey) {
    Set<String> names = new HashSet<>();
    Pattern pattern = Pattern.compile("%(.*?)%");
    Matcher matcher = pattern.matcher(input);
    while (matcher.find()) {

      String matchStr = matcher.group(1);
      if (matchStr.startsWith("variables")) {
        if (currentKey.toString().isEmpty()) {
          currentKey.append(matchStr.replaceFirst("variables.", ""));
        }
        names.add(matchStr.replaceFirst("variables.", ""));
        mapping.put(matchStr.replaceFirst("variables.", ""), matchStr);
      }

    }
    return names;
  }

  /**
   * 从description中获取指定的属性id，例如:洗烘房温度实际值异常，当前值是%varibles.a%，正常区间>%variables.la_bounds.lower%
   * <%variables.la_bounds.upper% .  需要返回a,a_bounds.lower,a_bounds.upper。返回格式为字符串包含 currentName
   * currentValue currentUpper currentLower description .
   */
  public @DataTypeHint("ROW<result STRING>")
  void eval(@DataTypeHint("STRING") String variables,
            @DataTypeHint("STRING") String description) {
    //从 description 中获取%xxx%格式的key
    JSONObject resultObj = new JSONObject();
    Set<String> keySet = null;
    Map<String, String> mapping = new HashMap<>();
    StringBuilder currentKey = new StringBuilder();
    String lowerKey = "";
    String upperKey = "";
    try {
      keySet = getVariableNames(description, mapping, currentKey);
      //获取lower和upper的key
      for (String key : keySet) {
        if (key.endsWith(".lower")) {
          lowerKey = key;
          break;
        }
      }
      for (String key : keySet) {
        if (key.endsWith(".upper")) {
          upperKey = key;
          break;
        }
      }
      JSONObject jsonObject = JSONObject.parseObject(variables);
      for (String key : keySet) {
        resultObj.put(key, JSONPath.eval(jsonObject, key));
      }
    } catch (Exception e) {
      rcLogError(String.format(
          "invoke VariablesBoundsTableRowFunction failed, variables:%s,description:%s,error msg:%s",
          variables, description, e.toString()));
      return;
    }

    //替换description中的占位符
    for (String key : resultObj.keySet()) {
      if (resultObj.getString(key) != null) {
        description = description.replaceAll("%(variables." + key + ")%", resultObj.getString(key));
      }
    }
    JSONObject outputObj = new JSONObject();
    outputObj.put("description", description);
    outputObj.put("currentUpper", resultObj.getString(upperKey));
    outputObj.put("currentLower", resultObj.getString(lowerKey));
    outputObj.put("currentName", currentKey);
    outputObj.put("currentValue", resultObj.get(currentKey.toString()));
    System.out.println(outputObj.toJSONString());
    collect(Row.of(outputObj.toJSONString()));

  }
}
