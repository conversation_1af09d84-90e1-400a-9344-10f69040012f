/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */
package com.rootcloud.analysis.udf.aggregate;

import com.rootcloud.analysis.udf.util.DateUtil;
import jdk.nashorn.internal.objects.annotations.Getter;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.AggregateFunction;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

public class StatusSumFunction
    extends AggregateFunction<Long, StatusSumFunction.StatusSumAccumulator> {

  @Override
  @DataTypeHint("BIGINT")
  public Long getValue(StatusSumAccumulator accumulator) {
    if (accumulator.getMap().isEmpty()) {
      return null;
    }
    List<String> dtLst = accumulator.getMap().keySet().stream()
        .sorted(String::compareTo).collect(Collectors.toList());
    long sum = 0;
    Integer preStatus = null;
    for (int i = 0; i < dtLst.size(); i++) {
      String dtStr = dtLst.get(i);
      Integer status = accumulator.getMap().get(dtStr);
      if (status == null) {
        continue;
      }
      if (preStatus != null) {
        String preDtStr = dtLst.get(i - 1);
        long diff = DateUtil.convertTimeToLong(dtStr) - DateUtil.convertTimeToLong(preDtStr);
        if (diff > 0) {
          sum += diff;
        }
      }
      if (status == accumulator.getBizStatus()) {
        preStatus = status;
      } else {
        preStatus = null;
      }
    }
    return sum / 1000;
  }

  @Override
  public StatusSumAccumulator createAccumulator() {
    return new StatusSumAccumulator();
  }

  public void accumulate(StatusSumAccumulator acc,
                  @DataTypeHint("STRING") String dt,
                  @DataTypeHint("INTEGER") Integer status,
                  @DataTypeHint("INTEGER") Integer bizStatus) {
    acc.getMap().put(dt, status);
    acc.setBizStatus(bizStatus);
  }

  public static class StatusSumAccumulator {

    private @DataTypeHint("MAP<STRING,INTEGER>")
    HashMap<String, Integer> map = new HashMap<>();

    private @DataTypeHint("INTEGER")
    int bizStatus;

    public HashMap<String, Integer> getMap() {
      return map;
    }

    public void setMap(HashMap<String, Integer> map) {
      this.map = map;
    }

    public int getBizStatus() {
      return bizStatus;
    }

    public void setBizStatus(int bizStatus) {
      this.bizStatus = bizStatus;
    }
  }

}
