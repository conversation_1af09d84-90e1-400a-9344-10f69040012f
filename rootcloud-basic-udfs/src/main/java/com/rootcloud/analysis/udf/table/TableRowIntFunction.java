/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.table;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.annotation.FunctionHint;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;

public class TableRowIntFunction extends TableFunction<Row> {

  public @DataTypeHint("ROW<deviceId STRING, dt BIGINT, la INTEGER, la_new INTEGER>")
  void eval(@DataTypeHint("STRING") String deviceId,
            @DataTypeHint(value = "TIMESTAMP(9)", bridgedTo = java.sql.Timestamp.class) Timestamp dt,
            @DataTypeHint("INTEGER") Integer la) {
    if (la == null) return;
    collect(Row.of(deviceId, dt.getTime(), la, la + 1));
    collect(Row.of(deviceId, dt.getTime(), la, la + 2));
    collect(Row.of(deviceId, dt.getTime(), la, la + 3));
  }

}
