package com.rootcloud.analysis.udf.scalar;

import com.rootcloud.analysis.udf.log.RcLogScalarFunction;
import com.rootcloud.analysis.udf.util.DateUtil;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.types.Row;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

public class LastInfoUdfF extends RcLogScalarFunction {
    // 设备ID -> (属性名 -> 上一次非空值及时间)
    private static final Map<String, ConcurrentHashMap<String, Tuple>> lastValueMap = new ConcurrentHashMap<>();

    // 线程安全的原子计数器，用于跟踪执行次数
    private static final AtomicLong executionCounter = new AtomicLong(0);

    // 用于存储已处理的时间戳，防止重复数据：设备ID -> (属性名 -> 已处理的时间戳集合)
    private static final Map<String, ConcurrentHashMap<String, Set<Long>>> processedTimestamps = new ConcurrentHashMap<>();


    // 内部类用于保存值和时间
    private static final class Tuple {
        BigDecimal value;
        Long time;
        Tuple(BigDecimal value, Long time) {
            this.value = value;
            this.time = time;
        }
    }

    @DataTypeHint("ROW<lastValue DECIMAL(38, 4), lastTime TIMESTAMP(3)>")
    public Row eval(
            @DataTypeHint("STRING") String deviceId,
            @DataTypeHint("STRING") String paramName,
            @DataTypeHint("DECIMAL(38, 4)") BigDecimal paramValue,
            @DataTypeHint("TIMESTAMP(3)") java.time.LocalDateTime time1
    ) {
        // 原子递增计数器并获取当前执行次数
        long currentCount = executionCounter.incrementAndGet();

        // 打印计数和传入的时间，用于对比数据时间的顺序性
        rcLogError("执行计数: " + currentCount + ", 传入时间: " + time1);

        if (deviceId == null || paramName == null) {
            return Row.of(null, null);
        }

        // 转换时间戳用于去重检查
        Long currentTimestamp = null;
        if (time1 != null) {
            currentTimestamp = time1.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        }

        // 获取该设备的时间戳集合Map，用于去重
        ConcurrentHashMap<String, Set<Long>> deviceTimestampMap = processedTimestamps.computeIfAbsent(deviceId, k -> new ConcurrentHashMap<>());
        Set<Long> paramTimestamps = deviceTimestampMap.computeIfAbsent(paramName, k -> ConcurrentHashMap.newKeySet());

        // 检查时间戳是否已经处理过
        boolean isTimestampDuplicate = false;
        if (currentTimestamp != null && !paramTimestamps.add(currentTimestamp)) {
            isTimestampDuplicate = true;
            rcLogError("检测到重复时间戳: " + currentTimestamp + " (设备: " + deviceId + ", 参数: " + paramName + ")");
        }

        // 获取该设备的属性Map
        ConcurrentHashMap<String, Tuple> deviceMap = lastValueMap.computeIfAbsent(deviceId, k -> new ConcurrentHashMap<>());
        Tuple lastTuple = deviceMap.get(paramName);
        if (lastTuple == null) {
            if (paramValue != null) {
                // 对于首次记录，如果不是重复时间戳，则正常记录时间
                Long ts = null;
                if (time1 != null && !isTimestampDuplicate) {
                    ts = currentTimestamp;
                }
                deviceMap.put(paramName, new Tuple(paramValue, ts));
                rcLogError("4====nowTuple.value " + deviceMap.get(paramName).value + " nowTuple.time " + deviceMap.get(paramName).time);
            }
            return Row.of(null, null);
        }
        Tuple tempLastTuple = new Tuple(lastTuple.value, lastTuple.time);
        rcLogError("1====tempLastTuple.value" + tempLastTuple.value + "tempLastTuple.time" + tempLastTuple.time);

        // 如果当前值非空，更新
        if (paramValue != null) {
            rcLogError("2====tempLastTuple.value " + tempLastTuple.value + " tempLastTuple.time " + tempLastTuple.time);

            // 如果是重复时间戳，只更新值，保持原有时间戳
            if (isTimestampDuplicate) {
                rcLogError("重复时间戳，只更新值，保持原时间: " + tempLastTuple.time);
                deviceMap.put(paramName, new Tuple(paramValue, tempLastTuple.time));
                return Row.of(paramValue, java.time.Instant.ofEpochMilli(tempLastTuple.time)
                        .atZone(java.time.ZoneId.systemDefault())
                        .toLocalDateTime());
            } else {
                // 非重复时间戳，正常更新值和时间
                deviceMap.put(paramName, new Tuple(paramValue, currentTimestamp));
            }

            rcLogError("3====tempLastTuple.value " + tempLastTuple.value + " tempLastTuple.time " + tempLastTuple.time);
            rcLogError("3====nowTuple.value " + deviceMap.get(paramName).value + " nowTuple.time " + deviceMap.get(paramName).time);
        }

        // 返回Row(lastValue, lastTime)
            if (tempLastTuple == null) {
                rcLogError("4====tempLastTuple is null " + tempLastTuple == null?"true":"false");
                return Row.of(null, null);
            } else {
                java.time.LocalDateTime lastTimeTemp = null;
                if (tempLastTuple.time != null) {
                    lastTimeTemp = java.time.Instant.ofEpochMilli(tempLastTuple.time)
                            .atZone(java.time.ZoneId.systemDefault())
                            .toLocalDateTime();
                }
                rcLogError("5====tempLastTuple.value " + tempLastTuple.value + " tempLastTuple.time " + tempLastTuple.time);
                return Row.of(tempLastTuple.value, lastTimeTemp);

            }
    }
    public static void main(String[] args) {
        LastInfoUdfF udf = new LastInfoUdfF();
        String deviceId = "dev1";
        String paramName = "p1";
        BigDecimal value1 = new BigDecimal("123.45");
        BigDecimal value2 = new BigDecimal("678.90");
        BigDecimal value4 = new BigDecimal("699.90");
        BigDecimal value5 = new BigDecimal("699.90");
        java.time.LocalDateTime ts1 = java.time.LocalDateTime.parse("2024-05-10T12:01:00.123");
        java.time.LocalDateTime ts2 = java.time.LocalDateTime.parse("2024-05-10T12:02:00.456");
        java.time.LocalDateTime ts3 = java.time.LocalDateTime.parse("2024-05-10T12:03:00.456");
        java.time.LocalDateTime ts4 = java.time.LocalDateTime.parse("2024-05-10T12:04:00.456");
        java.time.LocalDateTime ts5 = java.time.LocalDateTime.parse("2024-05-10T12:05:00.456");

        // 第一次调用，应该返回 Row.of(null, null)
        System.out.println("第一次: " + udf.eval(deviceId, paramName, value1, ts1));

        // 第二次调用，应该返回 Row.of(123.45, ts1)
        System.out.println("第二次: " + udf.eval(deviceId, paramName, null, null));

        // 第三次调用，paramValue为null，不更新，只返回上一次
        System.out.println("第三次: " + udf.eval(deviceId, paramName, null, null));

        // 第四次调用，paramValue为null，不更新，只返回上一次
        System.out.println("第四次: " + udf.eval(deviceId, paramName, value4, ts4));

        // 第五次调用，paramValue为null，不更新，只返回上一次
        System.out.println("第五次: " + udf.eval(deviceId, paramName, value5, ts5));

        // 测试重复时间戳 - 使用相同的 ts1
        BigDecimal duplicateValue = new BigDecimal("999.99");
        System.out.println("重复时间戳测试: " + udf.eval(deviceId, paramName, duplicateValue, ts1));

        // 再次使用相同时间戳
        BigDecimal duplicateValue2 = new BigDecimal("888.88");
        System.out.println("再次重复时间戳测试: " + udf.eval(deviceId, paramName, duplicateValue2, ts1));

        // 换一个属性名
        String paramName2 = "p2";
        BigDecimal value3 = new BigDecimal("555.55");
        java.time.LocalDateTime ts6 = java.time.LocalDateTime.parse("2024-05-10T12:02:00.789");
        System.out.println("新属性第一次: " + udf.eval(deviceId, paramName2, value3, ts6));
    }
}