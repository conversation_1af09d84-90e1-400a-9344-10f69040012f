package com.rootcloud.analysis.udf.scalar;

import com.rootcloud.analysis.udf.log.RcLogScalarFunction;
import com.rootcloud.analysis.udf.util.DateUtil;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.types.Row;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

public class LastInfoUdfF extends RcLogScalarFunction {
    // Device ID -> (Parameter Name -> Last non-null value and timestamp)
    private static final Map<String, ConcurrentHashMap<String, Tuple>> lastValueMap = new ConcurrentHashMap<>();

    // Thread-safe atomic counter for tracking execution count
    private static final AtomicLong executionCounter = new AtomicLong(0);

    // Store processed timestamps to prevent duplicate data: Device ID -> (Parameter Name -> Set of processed timestamps)
    private static final Map<String, ConcurrentHashMap<String, Set<Long>>> processedTimestamps = new ConcurrentHashMap<>();

    // Fine-grained locks: provide independent locks for each deviceId + paramName combination
    private static final Map<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();


    // Thread-safe immutable inner class for storing value and time
    private static final class Tuple {
        private final BigDecimal value;
        private final Long time;

        Tuple(BigDecimal value, Long time) {
            this.value = value;
            this.time = time;
        }

        public BigDecimal getValue() {
            return value;
        }

        public Long getTime() {
            return time;
        }

        @Override
        public String toString() {
            return "Tuple{value=" + value + ", time=" + time + "}";
        }
    }

    // Get lock for specific deviceId + paramName combination
    private static ReentrantLock getLock(String deviceId, String paramName) {
        String lockKey = deviceId + ":" + paramName;
        return lockMap.computeIfAbsent(lockKey, k -> new ReentrantLock());
    }

    @DataTypeHint("ROW<lastValue DECIMAL(38, 4), lastTime TIMESTAMP(3)>")
    public Row eval(
            @DataTypeHint("STRING") String deviceId,
            @DataTypeHint("STRING") String paramName,
            @DataTypeHint("DECIMAL(38, 4)") BigDecimal paramValue,
            @DataTypeHint("TIMESTAMP(3)") java.time.LocalDateTime time1
    ) {
        // Atomically increment counter and get current execution count
        long currentCount = executionCounter.incrementAndGet();

        // Print count and input time for comparing data time sequence
        rcLogError("Execution count: " + currentCount + ", Input time: " + time1);

        if (deviceId == null || paramName == null) {
            return Row.of(null, null);
        }

        // Convert timestamp for duplicate checking
        Long currentTimestamp = null;
        if (time1 != null) {
            currentTimestamp = time1.atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
        }

        // Get fine-grained lock to ensure atomic operations for specific deviceId + paramName
        ReentrantLock lock = getLock(deviceId, paramName);
        lock.lock();
        try {
            return processWithLock(deviceId, paramName, paramValue, currentTimestamp);
        } finally {
            lock.unlock();
        }
    }

    // Execute core business logic under lock protection
    private Row processWithLock(String deviceId, String paramName, BigDecimal paramValue, Long currentTimestamp) {
        // Get device timestamp set map for deduplication
        ConcurrentHashMap<String, Set<Long>> deviceTimestampMap = processedTimestamps.computeIfAbsent(deviceId, k -> new ConcurrentHashMap<>());
        Set<Long> paramTimestamps = deviceTimestampMap.computeIfAbsent(paramName, k -> ConcurrentHashMap.newKeySet());

        // Check if timestamp has been processed (atomic operation)
        boolean isTimestampDuplicate = false;
        if (currentTimestamp != null && !paramTimestamps.add(currentTimestamp)) {
            isTimestampDuplicate = true;
            rcLogError("Detected duplicate timestamp: " + currentTimestamp + " (device: " + deviceId + ", param: " + paramName + ")");
        }

        // Get device attribute map
        ConcurrentHashMap<String, Tuple> deviceMap = lastValueMap.computeIfAbsent(deviceId, k -> new ConcurrentHashMap<>());
        Tuple lastTuple = deviceMap.get(paramName);

        if (lastTuple == null) {
            if (paramValue != null) {
                // For first record, record time normally if not duplicate timestamp
                Long ts = null;
                if (currentTimestamp != null && !isTimestampDuplicate) {
                    ts = currentTimestamp;
                }
                Tuple newTuple = new Tuple(paramValue, ts);
                deviceMap.put(paramName, newTuple);
                rcLogError("4====First record newTuple.value " + newTuple.getValue() + " newTuple.time " + newTuple.getTime());
            }
            return Row.of(null, null);
        }

        // Create copy of current value for return
        Tuple tempLastTuple = new Tuple(lastTuple.getValue(), lastTuple.getTime());
        rcLogError("1====tempLastTuple.value " + tempLastTuple.getValue() + " tempLastTuple.time " + tempLastTuple.getTime());

        // If current value is not null, update
        if (paramValue != null) {
            rcLogError("2====tempLastTuple.value " + tempLastTuple.getValue() + " tempLastTuple.time " + tempLastTuple.getTime());

            // If duplicate timestamp, only update value, keep original timestamp
            if (isTimestampDuplicate) {
                rcLogError("Duplicate timestamp, only update value, keep original time: " + lastTuple.getTime());
                Tuple newTuple = new Tuple(paramValue, lastTuple.getTime());
                deviceMap.put(paramName, newTuple);

                // Return pre-update value and time
                if (tempLastTuple.getTime() != null) {
                    java.time.LocalDateTime lastTimeTemp = java.time.Instant.ofEpochMilli(tempLastTuple.getTime())
                            .atZone(java.time.ZoneId.systemDefault())
                            .toLocalDateTime();
                    return Row.of(tempLastTuple.getValue(), lastTimeTemp);
                } else {
                    return Row.of(tempLastTuple.getValue(), null);
                }
            } else {
                // Non-duplicate timestamp, update both value and time normally
                Tuple newTuple = new Tuple(paramValue, currentTimestamp);
                deviceMap.put(paramName, newTuple);
            }

            rcLogError("3====tempLastTuple.value " + tempLastTuple.getValue() + " tempLastTuple.time " + tempLastTuple.getTime());
            Tuple currentTuple = deviceMap.get(paramName);
            rcLogError("3====nowTuple.value " + currentTuple.getValue() + " nowTuple.time " + currentTuple.getTime());
        }

        // Return Row(lastValue, lastTime)
        java.time.LocalDateTime lastTimeTemp = null;
        if (tempLastTuple.getTime() != null) {
            lastTimeTemp = java.time.Instant.ofEpochMilli(tempLastTuple.getTime())
                    .atZone(java.time.ZoneId.systemDefault())
                    .toLocalDateTime();
        }
        rcLogError("5====tempLastTuple.value " + tempLastTuple.getValue() + " tempLastTuple.time " + tempLastTuple.getTime());
        return Row.of(tempLastTuple.getValue(), lastTimeTemp);
    }
    public static void main(String[] args) {
        LastInfoUdfF udf = new LastInfoUdfF();
        String deviceId = "dev1";
        String paramName = "p1";
        BigDecimal value1 = new BigDecimal("123.45");
        BigDecimal value2 = new BigDecimal("678.90");
        BigDecimal value4 = new BigDecimal("699.90");
        BigDecimal value5 = new BigDecimal("699.90");
        java.time.LocalDateTime ts1 = java.time.LocalDateTime.parse("2024-05-10T12:01:00.123");
        java.time.LocalDateTime ts2 = java.time.LocalDateTime.parse("2024-05-10T12:02:00.456");
        java.time.LocalDateTime ts3 = java.time.LocalDateTime.parse("2024-05-10T12:03:00.456");
        java.time.LocalDateTime ts4 = java.time.LocalDateTime.parse("2024-05-10T12:04:00.456");
        java.time.LocalDateTime ts5 = java.time.LocalDateTime.parse("2024-05-10T12:05:00.456");

        // First call, should return Row.of(null, null)
        System.out.println("First: " + udf.eval(deviceId, paramName, value1, ts1));

        // Second call, should return Row.of(123.45, ts1)
        System.out.println("Second: " + udf.eval(deviceId, paramName, null, null));

        // Third call, paramValue is null, no update, only return last
        System.out.println("Third: " + udf.eval(deviceId, paramName, null, null));

        // Fourth call, paramValue is not null, update and return last
        System.out.println("Fourth: " + udf.eval(deviceId, paramName, value4, ts4));

        // Fifth call, paramValue is not null, update and return last
        System.out.println("Fifth: " + udf.eval(deviceId, paramName, value5, ts5));

        // Test duplicate timestamp - use same ts1
        BigDecimal duplicateValue = new BigDecimal("999.99");
        System.out.println("Duplicate timestamp test: " + udf.eval(deviceId, paramName, duplicateValue, ts1));

        // Use same timestamp again
        BigDecimal duplicateValue2 = new BigDecimal("888.88");
        System.out.println("Another duplicate timestamp test: " + udf.eval(deviceId, paramName, duplicateValue2, ts1));

        // Change to another parameter name
        String paramName2 = "p2";
        BigDecimal value3 = new BigDecimal("555.55");
        java.time.LocalDateTime ts6 = java.time.LocalDateTime.parse("2024-05-10T12:02:00.789");
        System.out.println("New parameter first time: " + udf.eval(deviceId, paramName2, value3, ts6));
    }
}