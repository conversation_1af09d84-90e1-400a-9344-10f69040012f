/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.scalar;

import java.math.BigDecimal;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.ScalarFunction;

public class MaxFunction extends ScalarFunction {

  public @DataTypeHint("DECIMAL(38, 4)") BigDecimal eval(@DataTypeHint("DECIMAL(38, 4)") BigDecimal bigDecimal1,
                  @DataTypeHint("DECIMAL(38, 4)") BigDecimal bigDecimal2,
                  @DataTypeHint("DECIMAL(38, 4)") BigDecimal bigDecimal3) {
    if (bigDecimal1 == null && bigDecimal2 == null && bigDecimal3 == null) {
      return new BigDecimal(0);
    }
    if (bigDecimal1 == null) {
      bigDecimal1 = new BigDecimal(Integer.MIN_VALUE);
    }
    if (bigDecimal2 == null) {
      bigDecimal2 = new BigDecimal(Integer.MIN_VALUE);
    }
    if (bigDecimal3 == null) {
      bigDecimal3 = new BigDecimal(Integer.MIN_VALUE);
    }
    return new BigDecimal(Math.max(
                  Math.max(
                          bigDecimal1.doubleValue(), bigDecimal2.doubleValue()),
                  bigDecimal3.doubleValue()));
  }

}
