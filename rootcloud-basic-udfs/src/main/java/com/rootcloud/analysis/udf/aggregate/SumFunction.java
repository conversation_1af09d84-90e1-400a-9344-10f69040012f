/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */
package com.rootcloud.analysis.udf.aggregate;

import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.AggregateFunction;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

public class SumFunction
    extends AggregateFunction<BigDecimal, SumFunction.SumAccumulator> {

  @Override
  public @DataTypeHint("DECIMAL(20, 4)")
  BigDecimal getValue(SumAccumulator accumulator) {
    if (accumulator.getMap().isEmpty()) {
      return null;
    }
    List<String> dtLst = accumulator.getMap().keySet().stream()
        .sorted(String::compareTo).collect(Collectors.toList());
    BigDecimal sum = BigDecimal.ZERO;
    BigDecimal preValue = null;
    for (String dtStr :dtLst) {
      BigDecimal value = accumulator.getMap().get(dtStr);
      if (value == null) {
        continue;
      }
      if (preValue != null) {
        BigDecimal diff = value.subtract(preValue);
        if (diff.compareTo(BigDecimal.ZERO) > 0) {
          sum = sum.add(diff);
        }
      }
      preValue = value;
    }
    return sum;
  }

  @Override
  public SumAccumulator createAccumulator() {
    return new SumAccumulator();
  }

  public @DataTypeHint("DECIMAL(20, 4)")
  void accumulate(SumAccumulator acc,
                  @DataTypeHint("STRING") String dt,
                  @DataTypeHint("DECIMAL(20, 4)") BigDecimal la) {
    acc.getMap().put(dt, la);
  }

  public static class SumAccumulator {

    private @DataTypeHint("MAP<STRING,DECIMAL(20, 4)>")
    HashMap<String, BigDecimal> map = new HashMap<>();

    private @DataTypeHint("INTEGER")
    int bizStatus;

    public HashMap<String, BigDecimal> getMap() {
      return map;
    }

    public void setMap(HashMap<String, BigDecimal> map) {
      this.map = map;
    }

    public int getBizStatus() {
      return bizStatus;
    }

    public void setBizStatus(int bizStatus) {
      this.bizStatus = bizStatus;
    }
  }
}
