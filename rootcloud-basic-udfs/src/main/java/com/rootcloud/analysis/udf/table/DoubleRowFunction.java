/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.table;

import java.math.BigDecimal;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

public class DoubleRowFunction extends TableFunction<Row> {

  public @DataTypeHint("ROW<s STRING, bd1 DECIMAL(38, 4), bd2 DECIMAL(38, 4), bd3 DECIMAL(38, 4)>")
  void eval(@DataTypeHint("STRING") String deviceId,
            @DataTypeHint("DECIMAL(38, 4)") BigDecimal bigDecimal1,
            @DataTypeHint("DECIMAL(38, 4)") BigDecimal bigDecimal2,
            @DataTypeHint("DECIMAL(38, 4)") BigDecimal bigDecimal3) {
    collect(Row.of(deviceId, bigDecimal1, bigDecimal2, bigDecimal3));
    collect(Row.of(deviceId, bigDecimal1, bigDecimal2, bigDecimal3));
  }

}
