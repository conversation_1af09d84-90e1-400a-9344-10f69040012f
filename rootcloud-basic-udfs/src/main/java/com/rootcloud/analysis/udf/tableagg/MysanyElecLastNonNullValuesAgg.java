/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.tableagg;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.TableAggregateFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

public class MysanyElecLastNonNullValuesAgg
    extends TableAggregateFunction<Row, MysanyElecLastNonNullValuesAgg.LastNonNullValuesAccumulator> {

  @Override
  public LastNonNullValuesAccumulator createAccumulator() {

    return new LastNonNullValuesAccumulator();
  }

  public
  @DataTypeHint("ROW<"
          + "ts TIMESTAMP(3), "
          + "day_power_consumption DECIMAL(38, 6), "
          + "day_power_consumption_time BIGINT,"
          + "pump_motor_rotate_speed DECIMAL(38, 6), "
          + "pump_motor_rotate_speed_time BIGINT,"
          + "charging_status DECIMAL(38, 6), "
          + "charging_status_time BIGINT,"
          + "charge_time_remain DECIMAL(38, 6), "
          + "charge_time_remain_time BIGINT,"
          + "single_charge_capacity DECIMAL(38, 6), "
          + "single_charge_capacity_time BIGINT>")
  void accumulate(LastNonNullValuesAccumulator acc,
                  @DataTypeHint("TIMESTAMP(3)") Timestamp ts,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal day_power_consumption_context_this_pv,
                  @DataTypeHint("BIGINT") Long day_power_consumption_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal pump_motor_rotate_speed_context_this_pv,
                  @DataTypeHint("BIGINT") Long pump_motor_rotate_speed_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal charging_status_context_this_pv,
                  @DataTypeHint("BIGINT") Long charging_status_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal charge_time_remain_context_this_pv,
                  @DataTypeHint("BIGINT") Long charge_time_remain_context_this_pt,
                  @DataTypeHint("DECIMAL(38, 6)") BigDecimal single_charge_capacity_context_this_pv,
                  @DataTypeHint("BIGINT") Long single_charge_capacity_context_this_pt
                  ) {

    if (ts == null) {
      return;
    }
    acc.setTs(ts);

    if (day_power_consumption_context_this_pv != null) {
      acc.setPumpMotorRotateSpeed(day_power_consumption_context_this_pv);
      acc.setPumpMotorRotateSpeedTime(day_power_consumption_context_this_pt);
    }
    if (pump_motor_rotate_speed_context_this_pv != null) {
      acc.setChargingStatus(pump_motor_rotate_speed_context_this_pv);
      acc.setChargingStatusTime(pump_motor_rotate_speed_context_this_pt);
    }
    if (charging_status_context_this_pv != null) {
      acc.setChargeTimeRemain(charging_status_context_this_pv);
      acc.setChargeTimeRemainTime(charging_status_context_this_pt);
    }
    if (charge_time_remain_context_this_pv != null) {
      acc.setSingleChargeCapacity(charge_time_remain_context_this_pv);
      acc.setSingleChargeCapacityTime(charge_time_remain_context_this_pt);
    }
    if (single_charge_capacity_context_this_pv != null) {
      acc.setDayPowerConsumption(single_charge_capacity_context_this_pv);
      acc.setDayPowerConsumptionTime(single_charge_capacity_context_this_pt);
    }
  }

  public
  @DataTypeHint("ROW<"
          + "ts TIMESTAMP(3), "
          + "day_power_consumption DECIMAL(38, 6), "
          + "day_power_consumption_time BIGINT,"
          + "pump_motor_rotate_speed DECIMAL(38, 6), "
          + "pump_motor_rotate_speed_time BIGINT,"
          + "charging_status DECIMAL(38, 6), "
          + "charging_status_time BIGINT,"
          + "charge_time_remain DECIMAL(38, 6), "
          + "charge_time_remain_time BIGINT,"
          + "single_charge_capacity DECIMAL(38, 6), "
          + "single_charge_capacity_time BIGINT>")
  void emitValue(LastNonNullValuesAccumulator accumulator, Collector<Row> out) {
    Timestamp timestamp = accumulator.getTs();
    LocalDateTime localDateTime = timestamp.toLocalDateTime();
    out.collect(Row.of(
        localDateTime,
        accumulator.getPumpMotorRotateSpeed(),
        accumulator.getPumpMotorRotateSpeedTime(),
        accumulator.getChargingStatus(),
        accumulator.getChargingStatusTime(),
        accumulator.getChargeTimeRemain(),
        accumulator.getChargeTimeRemainTime(),
        accumulator.getSingleChargeCapacity(),
        accumulator.getSingleChargeCapacityTime(),
        accumulator.getDayPowerConsumption(),
        accumulator.getDayPowerConsumptionTime()
    ));
  }

  public
  @DataTypeHint("ROW<"
          + "ts TIMESTAMP(3), "
          + "day_power_consumption DECIMAL(38, 6), "
          + "day_power_consumption_time BIGINT,"
          + "pump_motor_rotate_speed DECIMAL(38, 6), "
          + "pump_motor_rotate_speed_time BIGINT,"
          + "charging_status DECIMAL(38, 6), "
          + "charging_status_time BIGINT,"
          + "charge_time_remain DECIMAL(38, 6), "
          + "charge_time_remain_time BIGINT,"
          + "single_charge_capacity DECIMAL(38, 6), "
          + "single_charge_capacity_time BIGINT>")
  void retract(LastNonNullValuesAccumulator acc,
               @DataTypeHint("TIMESTAMP(3)") Timestamp ts,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal day_power_consumption_context_this_pv,
               @DataTypeHint("BIGINT") Long day_power_consumption_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal pump_motor_rotate_speed_context_this_pv,
               @DataTypeHint("BIGINT") Long pump_motor_rotate_speed_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal charging_status_context_this_pv,
               @DataTypeHint("BIGINT") Long charging_status_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal charge_time_remain_context_this_pv,
               @DataTypeHint("BIGINT") Long charge_time_remain_context_this_pt,
               @DataTypeHint("DECIMAL(38, 6)") BigDecimal single_charge_capacity_context_this_pv,
               @DataTypeHint("BIGINT") Long single_charge_capacity_context_this_pt
               ) {

  }

  public void merge(LastNonNullValuesAccumulator acc,
                    Iterable<LastNonNullValuesAccumulator> iterable) {
    for (LastNonNullValuesAccumulator otherAcc : iterable) {
      acc.setTs(otherAcc.getTs());

      acc.setPumpMotorRotateSpeed(otherAcc.getPumpMotorRotateSpeed());
      acc.setPumpMotorRotateSpeedTime(otherAcc.getPumpMotorRotateSpeedTime());

      acc.setChargingStatus(otherAcc.getChargingStatus());
      acc.setChargingStatusTime(otherAcc.getChargingStatusTime());

      acc.setChargeTimeRemain(otherAcc.getChargeTimeRemain());
      acc.setChargeTimeRemainTime(otherAcc.getChargeTimeRemainTime());

      acc.setSingleChargeCapacity(otherAcc.getSingleChargeCapacity());
      acc.setSingleChargeCapacityTime(otherAcc.getSingleChargeCapacityTime());

      acc.setDayPowerConsumption(otherAcc.getDayPowerConsumption());
      acc.setDayPowerConsumptionTime(otherAcc.getDayPowerConsumptionTime());
    }
  }

  public static class LastNonNullValuesAccumulator {
    private @DataTypeHint("TIMESTAMP(3)")
    Timestamp ts;

    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal pump_motor_rotate_speed;
    private @DataTypeHint("BIGINT")
    Long pump_motor_rotate_speed_time;

    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal charging_status;
    private @DataTypeHint("BIGINT")
    Long charging_status_time;

    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal charge_time_remain;
    private @DataTypeHint("BIGINT")
    Long charge_time_remain_time;

    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal single_charge_capacity;
    private @DataTypeHint("BIGINT")
    Long single_charge_capacity_time;

    private @DataTypeHint("DECIMAL(38, 6)")
    BigDecimal day_power_consumption;
    private @DataTypeHint("BIGINT")
    Long day_power_consumption_time;


    public Timestamp getTs() {
      return ts;
    }

    public void setTs(Timestamp ts) {
      this.ts = ts;
    }

    public BigDecimal getPumpMotorRotateSpeed() {
      return pump_motor_rotate_speed;
    }

    public void setPumpMotorRotateSpeed(BigDecimal pump_motor_rotate_speed) {
      this.pump_motor_rotate_speed = pump_motor_rotate_speed;
    }

    public Long getPumpMotorRotateSpeedTime() {
      return pump_motor_rotate_speed_time;
    }

    public void setPumpMotorRotateSpeedTime(Long pump_motor_rotate_speed_time) {
      this.pump_motor_rotate_speed_time = pump_motor_rotate_speed_time;
    }

    public BigDecimal getChargingStatus() {
      return charging_status;
    }

    public void setChargingStatus(BigDecimal charging_status) {
      this.charging_status = charging_status;
    }

    public Long getChargingStatusTime() {
      return charging_status_time;
    }

    public void setChargingStatusTime(Long charging_status_time) {
      this.charging_status_time = charging_status_time;
    }

    public BigDecimal getChargeTimeRemain() {
      return charge_time_remain;
    }

    public void setChargeTimeRemain(BigDecimal charge_time_remain) {
      this.charge_time_remain = charge_time_remain;
    }

    public Long getChargeTimeRemainTime() {
      return charge_time_remain_time;
    }

    public void setChargeTimeRemainTime(Long charge_time_remain_time) {
      this.charge_time_remain_time = charge_time_remain_time;
    }

    public BigDecimal getSingleChargeCapacity() {
      return single_charge_capacity;
    }

    public void setSingleChargeCapacity(BigDecimal single_charge_capacity) {
      this.single_charge_capacity = single_charge_capacity;
    }

    public Long getSingleChargeCapacityTime() {
      return single_charge_capacity_time;
    }

    public void setSingleChargeCapacityTime(Long single_charge_capacity_time) {
      this.single_charge_capacity_time = single_charge_capacity_time;
    }

    public BigDecimal getDayPowerConsumption() {
      return day_power_consumption;
    }

    public void setDayPowerConsumption(BigDecimal day_power_consumption) {
      this.day_power_consumption = day_power_consumption;
    }

    public Long getDayPowerConsumptionTime() {
      return day_power_consumption_time;
    }

    public void setDayPowerConsumptionTime(Long day_power_consumption_time) {
      this.day_power_consumption_time = day_power_consumption_time;
    }
  }


}