/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.scalar;

import com.alibaba.fastjson.JSONObject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.ScalarFunction;

public class LocationAbnormalDetailNew extends ScalarFunction {

  public @DataTypeHint("STRING") String eval(
      @DataTypeHint("BIGINT") Long this_pt,
      @DataTypeHint("BIGINT") Long last_pt,
      @DataTypeHint("DECIMAL(38, 6)") BigDecimal this_longitude,
      @DataTypeHint("DECIMAL(38, 6)") BigDecimal this_latitude,
      @DataTypeHint("DECIMAL(38, 6)") BigDecimal last_longitude,
      @DataTypeHint("DECIMAL(38, 6)") BigDecimal last_latitude) {


    if(this_longitude == null && this_latitude == null) {

      return null;
    }

    // 设置精度为小数点后三位，并使用四舍五入模式
    BigDecimal roundedThisLongitude;
    BigDecimal roundedThisLatitude;
    JSONObject result = new JSONObject();

    // 如果当前经纬度不同时为空，且有一个为空,需要去做经纬度异常记录
    if(this_longitude == null) {
      roundedThisLatitude = this_latitude.setScale(3, RoundingMode.HALF_UP);
      result.put("this_longitude", null);
      result.put("this_latitude", roundedThisLatitude);
      result.put("this_pt", this_pt);
      return result.toJSONString();
    }
    if(this_latitude == null) {
      roundedThisLongitude = this_longitude.setScale(3, RoundingMode.HALF_UP);
      result.put("this_longitude", roundedThisLongitude);
      result.put("this_latitude", null);
      result.put("this_pt", this_pt);
      return result.toJSONString();
    }

    // 经纬度都不为空
    roundedThisLongitude = this_longitude.setScale(3, RoundingMode.HALF_UP);
    roundedThisLatitude = this_latitude.setScale(3, RoundingMode.HALF_UP);

    // 如果当前经纬为0，那么就不计算漂移，但是需要判断为0异常
    if(this_longitude.compareTo(BigDecimal.ZERO) == 0 || this_latitude.compareTo(BigDecimal.ZERO) == 0){
      result.put("this_longitude", roundedThisLongitude);
      result.put("this_latitude", roundedThisLatitude);
      result.put("this_pt", this_pt);
      return result.toJSONString();
    }

    if(last_longitude == null || last_latitude == null) {
      result.put("this_longitude", roundedThisLongitude);
      result.put("this_latitude", roundedThisLatitude);
      result.put("this_pt", this_pt);
      return result.toJSONString();
    }
    BigDecimal roundedLastLongitude = last_longitude.setScale(3, RoundingMode.HALF_UP);
    BigDecimal roundedLastLatitude = last_latitude.setScale(3, RoundingMode.HALF_UP);
    // 如果前值经纬为0，那么就不计算漂移，但是需要判断为0异常
    if(last_longitude.compareTo(BigDecimal.ZERO) == 0 || last_latitude.compareTo(BigDecimal.ZERO) == 0){
      result.put("this_longitude", roundedThisLongitude);
      result.put("this_latitude", roundedThisLatitude);
      result.put("this_pt", this_pt);
      return result.toJSONString();
    }

    if(this_longitude.compareTo(new BigDecimal("180")) > 0
        || this_longitude.compareTo(new BigDecimal("-180")) < 0
        || this_latitude.compareTo(new BigDecimal("90")) > 0
        || this_latitude.compareTo(new BigDecimal("-90")) < 0
        || last_longitude.compareTo(new BigDecimal("180")) > 0
        || last_longitude.compareTo(new BigDecimal("-180")) < 0
        || last_latitude.compareTo(new BigDecimal("90")) > 0
        || last_latitude.compareTo(new BigDecimal("-90")) < 0) {

      result.put("this_longitude", roundedThisLongitude);
      result.put("this_latitude", roundedThisLatitude);
      result.put("this_pt", this_pt);
      return result.toJSONString();
    }

    double tmp1 = Math.sin(this_latitude.doubleValue()/57.29578);//this_latitude是BigDecimal，请修改

    double tmp2 = Math.sin(last_latitude.doubleValue()/57.29578);
    double tmp3 = Math.cos(this_latitude.doubleValue()/57.29578);
    double tmp4 = Math.cos(last_latitude.doubleValue()/57.29578);
    double tmp5 = Math.cos((this_longitude.doubleValue()-last_longitude.doubleValue())/57.29578);
    double tmp6 = tmp1 * tmp2 + tmp3 * tmp4 * tmp5;
    /* 计算相邻经纬度的地球距离，单位km */
    double gps_distance = 6371.393*Math.acos(tmp6);
    /* 计算相邻经纬度的时间差，单位ms */
    Long gps_duration = this_pt - last_pt;
    /*20240219新增，如果2条数据的时间差，大于11s，那么忽略漂移计算*/
    /*202411126修改，如果2条数据的时间差，大于3600s，那么忽略漂移计算
    * 如果小于等于3600s大于10s，按实际时间间隔计算
    * 如果小于等于10s，按10s计算*/
    if(gps_duration > 1000*3600 )
    {
      result.put("this_longitude", roundedThisLongitude);
      result.put("this_latitude", roundedThisLatitude);
      result.put("this_pt", this_pt);
      return result.toJSONString();
    }
    Long compute_duration = 0l;
    if(1000*10 < gps_duration && gps_duration <= 1000*3600 )
    {
      compute_duration = gps_duration;
    } else if (gps_duration <= 1000*10) {
      compute_duration = 1000*10l;
    }
    /* 20240219修改 计算相邻经纬度的速度，单位km/h，固定按照10s计算速度 */
    double gps_speed = (compute_duration == 0)?0:(3600*1000*gps_distance/(compute_duration));

    result.put("this_longitude", roundedThisLongitude);
    result.put("this_latitude", roundedThisLatitude);
    result.put("this_pt", this_pt);
    result.put("last_longitude", roundedLastLongitude);
    result.put("last_latitude", roundedLastLatitude);
    result.put("last_pt", last_pt);
    result.put("pt_inc", compute_duration);
    result.put("distance", gps_distance);
    result.put("speed", gps_speed);
    return result.toJSONString();

  }


}
