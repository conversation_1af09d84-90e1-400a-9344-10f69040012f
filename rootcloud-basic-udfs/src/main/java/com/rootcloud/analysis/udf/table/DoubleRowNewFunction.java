/*
 * Licensed Materials - Property of ROOTCLOUD
 * THIS MODULE IS "RESTRICTED MATERIALS OF ROOTCLOUD"
 * (c) Copyright ROOTCLOUD Inc. 2019 All Rights Reserved
 *
 * The source code for this program is not published or
 * otherwise divested of its trade secrets
 */

package com.rootcloud.analysis.udf.table;

import java.math.BigDecimal;
import org.apache.flink.table.annotation.DataTypeHint;
import org.apache.flink.table.functions.TableFunction;
import org.apache.flink.types.Row;

public class DoubleRowNewFunction extends TableFunction<Row> {

  public @DataTypeHint("ROW<workStatus INTEGER, bd1 DECIMAL(38, 4), bd2 DECIMAL(38, 4), bd3 DECIMAL(38, 4)>")
  void eval(@DataTypeHint("INTEGER") Integer workStatus,
            @DataTypeHint("DECIMAL(38, 4)") BigDecimal bigDecimal1,
            @DataTypeHint("DECIMAL(38, 4)") BigDecimal bigDecimal2,
            @DataTypeHint("DECIMAL(38, 4)") BigDecimal bigDecimal3) {
    BigDecimal num = new BigDecimal(0.11D);
    collect(Row.of(new Object[]{workStatus == null ? 100 : workStatus + 100,
                                bigDecimal1 == null ? num : bigDecimal1.add(num),
                                bigDecimal2 == null ? num : bigDecimal2.add(num),
                                bigDecimal3 == null ? num : bigDecimal3.add(num)}));

    collect(Row.of(new Object[]{workStatus == null ? 200 : workStatus + 200,
            bigDecimal1 == null ? num.multiply(new BigDecimal(2))
                    : bigDecimal1.add(num.multiply(new BigDecimal(2))),
            bigDecimal2 == null ? num.multiply(new BigDecimal(2))
                    : bigDecimal2.add(num.multiply(new BigDecimal(2))),
            bigDecimal3 == null ? num.multiply(new BigDecimal(2))
                    : bigDecimal3.add(num.multiply(new BigDecimal(2)))}));

    collect(Row.of(new Object[]{workStatus == null ? 300 : workStatus + 300,
            bigDecimal1 == null ? num.multiply(new BigDecimal(3))
                    : bigDecimal1.add(num.multiply(new BigDecimal(3))),
            bigDecimal2 == null ? num.multiply(new BigDecimal(3))
                    : bigDecimal2.add(num.multiply(new BigDecimal(3))),
            bigDecimal3 == null ? num.multiply(new BigDecimal(3))
                    : bigDecimal3.add(num.multiply(new BigDecimal(3)))}));
  }

}
