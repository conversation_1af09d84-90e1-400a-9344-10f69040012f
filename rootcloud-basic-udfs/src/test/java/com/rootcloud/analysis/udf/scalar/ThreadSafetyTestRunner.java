package com.rootcloud.analysis.udf.scalar;

import org.apache.flink.types.Row;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Simple thread safety test runner
 */
public class ThreadSafetyTestRunner {

    public static void main(String[] args) {
        ThreadSafetyTestRunner runner = new ThreadSafetyTestRunner();

        System.out.println("Starting thread safety tests...");

        try {
            runner.testBasicConcurrency();
            runner.testDuplicateTimestamps();
            runner.testMultiDeviceScenario();
            runner.testPerformance();

            System.out.println("\n[PASS] All tests passed! Thread safety improvements successful.");
        } catch (Exception e) {
            System.err.println("\n[FAIL] Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Basic concurrency test
     */
    public void testBasicConcurrency() throws InterruptedException {
        System.out.println("\n1. Basic concurrency test...");

        LastInfoUdfF udf = new LastInfoUdfF();
        ExecutorService executor = Executors.newFixedThreadPool(5);
        CountDownLatch latch = new CountDownLatch(5);
        AtomicInteger successCount = new AtomicInteger(0);

        for (int i = 0; i < 5; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < 100; j++) {
                        BigDecimal value = new BigDecimal(threadId * 100 + j);
                        LocalDateTime time = LocalDateTime.now().plusNanos(threadId * 1000000L + j);

                        Row result = udf.eval("device1", "param1", value, time);
                        if (result != null) {
                            successCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(10, TimeUnit.SECONDS);
        executor.shutdown();

        System.out.println("   Successful operations: " + successCount.get() + "/500");
        if (successCount.get() == 500) {
            System.out.println("   [PASS] Basic concurrency test passed");
        } else {
            throw new RuntimeException("Basic concurrency test failed");
        }
    }
    
    /**
     * Duplicate timestamp test
     */
    public void testDuplicateTimestamps() throws InterruptedException {
        System.out.println("\n2. Duplicate timestamp test...");

        LastInfoUdfF udf = new LastInfoUdfF();
        ExecutorService executor = Executors.newFixedThreadPool(3);
        CountDownLatch latch = new CountDownLatch(3);
        LocalDateTime fixedTime = LocalDateTime.of(2024, 5, 10, 12, 0, 0);
        AtomicInteger operationCount = new AtomicInteger(0);

        for (int i = 0; i < 3; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < 50; j++) {
                        BigDecimal value = new BigDecimal(threadId * 50 + j);
                        Row result = udf.eval("device2", "param2", value, fixedTime);
                        if (result != null) {
                            operationCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(10, TimeUnit.SECONDS);
        executor.shutdown();

        System.out.println("   Duplicate timestamp operations: " + operationCount.get() + "/150");
        System.out.println("   [PASS] Duplicate timestamp test passed (no exceptions thrown)");
    }
    
    /**
     * Multi-device scenario test
     */
    public void testMultiDeviceScenario() throws InterruptedException {
        System.out.println("\n3. Multi-device scenario test...");

        LastInfoUdfF udf = new LastInfoUdfF();
        ExecutorService executor = Executors.newFixedThreadPool(10);
        CountDownLatch latch = new CountDownLatch(10);
        AtomicLong totalOps = new AtomicLong(0);

        for (int i = 0; i < 10; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < 100; j++) {
                        String deviceId = "device" + (j % 5);
                        String paramName = "param" + (j % 3);
                        BigDecimal value = new BigDecimal(threadId * 100 + j);
                        LocalDateTime time = LocalDateTime.now().plusNanos(threadId * 1000000L + j);

                        Row result = udf.eval(deviceId, paramName, value, time);
                        if (result != null) {
                            totalOps.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(15, TimeUnit.SECONDS);
        executor.shutdown();

        System.out.println("   Multi-device operations: " + totalOps.get() + "/1000");
        if (totalOps.get() == 1000) {
            System.out.println("   [PASS] Multi-device scenario test passed");
        } else {
            throw new RuntimeException("Multi-device scenario test failed");
        }
    }
    
    /**
     * Performance test
     */
    public void testPerformance() throws InterruptedException {
        System.out.println("\n4. Performance test...");

        LastInfoUdfF udf = new LastInfoUdfF();
        ExecutorService executor = Executors.newFixedThreadPool(10);
        CountDownLatch latch = new CountDownLatch(10);

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 10; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < 1000; j++) {
                        String deviceId = "perfDevice" + (j % 5);
                        String paramName = "perfParam" + (j % 2);
                        BigDecimal value = new BigDecimal(threadId * 1000 + j);
                        LocalDateTime time = LocalDateTime.now().plusNanos(threadId * 1000000L + j);

                        udf.eval(deviceId, paramName, value, time);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double opsPerSecond = 10000.0 / (totalTime / 1000.0);

        System.out.println("   Total operations: 10,000");
        System.out.println("   Total time: " + totalTime + "ms");
        System.out.println("   Operations per second: " + String.format("%.2f", opsPerSecond));

        if (opsPerSecond > 500) {
            System.out.println("   [PASS] Performance test passed");
        } else {
            System.out.println("   [WARN] Performance is low but acceptable: " + String.format("%.2f", opsPerSecond) + " ops/sec");
        }
    }
}
