plugins {
    id 'java-library'
    id 'com.github.johnrengelman.shadow' version '4.0.3'
}

sourceCompatibility = 1.8
targetCompatibility = 1.8

repositories {
    mavenLocal()
//    maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
//    maven { url "https://maven.aliyun.com/repository/public/" }
    maven { url "https://nexus.rootcloudapp.com/repository/maven-releases/" }
    maven {
        url "${nexusUrl}/repository/maven-releases/"
        credentials {
            username nexusUsername
            password nexusPassword
        }
        authentication {
            basic(BasicAuthentication)
        }
    }
    mavenCentral()
}

dependencies {
    implementation group: 'org.apache.flink', name: 'flink-table-common', version: "${flinkVersion}"
    implementation group: 'com.google.guava', name: 'guava', version: "${guavaVersion}"
    implementation group: 'joda-time', name: 'joda-time', version: "${jodaTimeVersion}"
    implementation 'com.rootcloud:analysis-udf-log:1.0.4'
// https://mvnrepository.com/artifact/com.alibaba/fastjson
    implementation group: 'com.alibaba', name: 'fastjson', version: '2.0.27'


}
apply plugin: 'com.github.johnrengelman.shadow'

shadowJar {
    mergeServiceFiles()
    dependencies {
        include(dependency("com.rootcloud:analysis-udf-log:1.0.4"))
    }
}